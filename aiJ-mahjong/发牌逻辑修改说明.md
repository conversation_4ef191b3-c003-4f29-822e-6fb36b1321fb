# 发牌逻辑修改说明

## 修改目标

确保庄家在游戏开始时的前3张牌是中发白（中、发、白各一张），后续进入正常随机发牌流程。

## 修改内容

### 1. 新增 `createSpecialDeckForYangTest()` 方法

**位置**: `MahjongTableAbility.java` 第371-437行

**功能**: 创建特殊牌堆，确保庄家在初始发牌阶段就能获得中发白三张牌

**实现逻辑**:
1. 先对所有牌进行正常洗牌
2. 从洗牌后的牌堆中移除中发白各一张
3. 计算庄家13张牌在牌堆中的位置范围
4. 将中发白三张牌放置在庄家的前3张牌位置
5. 返回修改后的牌堆

### 2. 修改 `onGameReset()` 方法

**位置**: `MahjongTableAbility.java` 第322-344行

**修改内容**:
- 将原来的随机洗牌改为调用 `createSpecialDeckForYangTest()` 方法
- 添加测试模式说明日志

### 3. 修改 `onGameStart()` 方法

**位置**: `MahjongTableAbility.java` 第451-474行

**修改内容**:
- 在庄家初始发牌完成后，验证是否成功获得中发白三张牌
- 添加详细的验证日志输出

### 4. 简化 `dispatchCard()` 方法

**位置**: `MahjongTableAbility.java` 第533-542行

**修改内容**:
- 移除了复杂的庄家发牌验证逻辑
- 简化为通用的发牌记录日志

## 发牌流程分析

### 原始发牌流程
1. `onGameStart()` 中给每个玩家发13张牌（从牌堆末尾开始）
2. 调用 `dispatchCard(banker, false)` 给庄家发第14张牌
3. 后续游戏中通过 `dispatchCard()` 继续发牌

### 修改后的发牌流程
1. `onGameReset()` 创建特殊牌堆，庄家的前3张牌被设置为中发白
2. `onGameStart()` 给每个玩家发13张牌时，庄家自动获得中发白三张牌
3. 验证庄家是否成功获得中发白
4. 后续发牌流程保持不变

## 牌堆位置计算

假设有4个玩家，总牌数136张：

```
总牌数: 136张
每人初始牌数: 13张
总初始发牌: 4 × 13 = 52张
剩余牌数: 136 - 52 = 84张

发牌顺序（从牌堆末尾开始）:
- 玩家3: 位置 [123, 136)
- 玩家2: 位置 [110, 123)  
- 玩家1: 位置 [97, 110)
- 玩家0(庄家): 位置 [84, 97)

庄家前3张牌位置: [84, 87)
```

## 测试验证

### 日志输出示例

```
🧪 测试模式：庄家前3张牌将是中发白，后续随机发牌
🧪 特殊牌堆创建完成，庄家前3张牌设置为中发白
🧪 庄家牌位置范围：[84, 97), 前3张牌：[84]=0x35, [85]=0x36, [86]=0x37
🧪 庄家初始发牌完成，验证中发白:
  中(0x35): 1张
  发(0x36): 1张  
  白(0x37): 1张
🧪 ✅ 庄家在初始发牌阶段成功获得中发白三张牌！
```

### 验证要点

1. **牌堆创建**: 确认中发白被正确放置在庄家的前3张牌位置
2. **初始发牌**: 验证庄家在13张初始牌中包含中发白各一张
3. **仰操作**: 庄家发第14张牌后应该能够检测到仰操作

## 恢复正常模式

要恢复正常随机发牌模式，只需在 `onGameReset()` 方法中：

1. 注释掉测试模式代码：
   ```java
   // repertoryCard = createSpecialDeckForYangTest();
   ```

2. 启用正常模式代码：
   ```java
   repertoryCard = MahjongKit.shuffle(MahjongConst.CARDS, 2);
   ```

## 注意事项

1. **庄家确定**: 庄家通过掷骰子确定，可能不是玩家0
2. **牌堆完整性**: 确保移除和添加中发白的数量一致，保持牌堆总数不变
3. **随机性**: 除了庄家的前3张牌，其他牌的分布仍然是随机的
4. **兼容性**: 修改不影响其他游戏逻辑，只是改变了初始牌的分布
