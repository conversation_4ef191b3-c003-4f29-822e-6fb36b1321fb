# 仰操作面牌显示修复说明

## 问题描述

仰操作触发后，在展示面牌时显示的是背面，而不是像吃碰杠一样显示正面，导致玩家无法看到仰牌的具体牌型（中发白三张牌）。

## 问题原因

在 `MahjongGameLayer.ts` 的面牌渲染方法中，所有方向的渲染函数都缺少对 `MahjongWeaveType.Y`（仰操作）的处理：

1. **组件选择阶段**：仰操作的 `weaveType` 是 4，但 switch 语句只处理了吃(1)、碰(2)、杠(3)
2. **牌面设置阶段**：没有为仰操作设置具体的中发白牌面图片
3. **默认处理**：仰操作走到 `default` 分支，使用碰牌组件但不设置牌面，导致显示背面

## 修复方案

### 1. 组件选择修复

为每个方向的面牌渲染添加仰操作的组件选择：

```typescript
case MahjongWeaveType.Y:  // 仰牌
    _weaveComponent = fgui.UIPackage.createObject("mahjong", "SouthPengComponent").asCom;
    break;
```

### 2. 牌面显示修复

为每个方向添加仰操作的牌面设置，显示中发白三张牌：

```typescript
case MahjongWeaveType.Y:  // 仰牌 - 显示中发白三张牌
    _weaveComponent.getChild("n0").asLoader.url = fgui.UIPackage.createObject("mahjong", "s_mingmah_35").asImage.resourceURL; // 中
    _weaveComponent.getChild("n1").asLoader.url = fgui.UIPackage.createObject("mahjong", "s_mingmah_36").asImage.resourceURL; // 发
    _weaveComponent.getChild("n2").asLoader.url = fgui.UIPackage.createObject("mahjong", "s_mingmah_37").asImage.resourceURL; // 白
    break;
```

## 修复的方法

### 1. `renderWestCard()` - 西面玩家
- **位置**: 第273-290行（组件选择）
- **位置**: 第316-328行（牌面设置）
- **资源前缀**: `w_mingmah_`

### 2. `renderNorthCard()` - 北面玩家  
- **位置**: 第370-387行（组件选择）
- **位置**: 第413-425行（牌面设置）
- **资源前缀**: `s_mingmah_`

### 3. `renderEastCard()` - 东面玩家
- **位置**: 第459-476行（组件选择）
- **位置**: 第502-514行（牌面设置）
- **资源前缀**: `e_mingmah_`

### 4. `renderSouthCard()` - 南面玩家（自己）
- **位置**: 第557-574行（组件选择）
- **位置**: 第600-612行（牌面设置）
- **资源前缀**: `s_mingmah_`

## 牌面资源说明

### 中发白牌值对应关系
- **中**: 0x35 → `mingmah_35`
- **发**: 0x36 → `mingmah_36`  
- **白**: 0x37 → `mingmah_37`

### 不同方向的资源前缀
- **南面（玩家自己）**: `s_mingmah_`
- **北面（对面玩家）**: `s_mingmah_`
- **东面（右侧玩家）**: `e_mingmah_`
- **西面（左侧玩家）**: `w_mingmah_`

## 修复效果

修复后，仰操作的面牌将：

1. ✅ **正确显示牌面**：显示中发白三张牌的正面图案
2. ✅ **统一显示风格**：与吃碰杠操作保持一致的显示效果
3. ✅ **清晰的牌型识别**：玩家可以清楚看到仰操作使用的是中发白三张牌
4. ✅ **全方向支持**：所有方向的玩家仰操作都能正确显示

## 测试验证

### 验证要点
1. **触发仰操作**：确保庄家能够成功触发仰操作
2. **面牌显示**：验证仰操作后面牌显示为中发白三张正面牌
3. **多方向测试**：测试不同位置玩家的仰操作显示效果
4. **与其他操作对比**：确认仰操作显示效果与吃碰杠一致

### 预期结果
- 仰操作完成后，面牌区域应显示三张正面的中发白牌
- 显示效果应与碰牌类似，但牌面内容为中(红)、发(绿)、白(白板)
- 所有方向的玩家仰操作都应有相同的显示效果

## 相关文件

- **主要修复文件**: `aiJ-client/assets/Script/room/mahjong/MahjongGameLayer.ts`
- **相关枚举**: `aiJ-client/assets/Script/room/mahjong/struct/MahjongWeaveType.ts`
- **数据结构**: `aiJ-client/assets/Script/room/mahjong/struct/MahjongWeaveItem.ts`

## 注意事项

1. **资源依赖**: 确保游戏资源包中包含中发白的牌面图片资源
2. **组件复用**: 仰操作复用碰牌组件，因为都是三张牌的显示
3. **一致性**: 保持与现有吃碰杠操作相同的显示逻辑和风格
