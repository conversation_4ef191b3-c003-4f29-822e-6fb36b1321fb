window.__require=function e(t,n,r){function o(a,s){if(!n[a]){if(!t[a]){var u=a.split("/");if(u=u[u.length-1],!t[u]){var c="function"==typeof __require&&__require;if(!s&&c)return c(u,!0);if(i)return i(u,!0);throw new Error("Cannot find module '"+a+"'")}a=u}var f=n[a]={exports:{}};t[a][0].call(f.exports,function(e){return o(t[a][1][e]||e)},f,f.exports,e,t,n,r)}return n[a].exports}for(var i="function"==typeof __require&&__require,a=0;a<r.length;a++)o(r[a]);return o}({AbstractRoomConfig:[function(e,t,n){"use strict";cc._RF.push(t,"fe2ea2y47tJ9LHTi2h2jaZ4","AbstractRoomConfig"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("../ws/AiJ"),o=e("../ws/AiJKit"),i=e("../AppConfig"),a=function(){function e(t,n){this.host=t,this.port=n,this.url="ws://"+t+":"+n,this._config=new r.AiJ.Config(this.url,new r.AiJ.Options),e.destroyInst(),e.createInst(this)}return e.getInst=function(){return e._inst},e.destroyInst=function(){null!=e._inst&&(e._inst.destroy(),e._inst=null)},e.createInst=function(t){e._inst=t,e._inst.create()},e.prototype.create=function(){this.onCreate(),o.default.init(i.default.GAME_WS_NAME,this._config)},e.prototype.destroy=function(){o.default.close(i.default.GAME_WS_NAME),this.onDestroy()},e}();n.default=a,cc._RF.pop()},{"../AppConfig":"AppConfig","../ws/AiJ":"AiJ","../ws/AiJKit":"AiJKit"}],AiJApp:[function(e,t,n){"use strict";cc._RF.push(t,"45dc7jmSU9FfJq1niV8UwFC","AiJApp");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var i=cc._decorator.ccclass,a=e("./WelcomeLayer"),s=e("./UIManger"),u=e("./AppConfig"),c=e("./fire/FireKit"),f=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}var n;return r(t,e),n=t,t.prototype.onLoad=function(){var e=this;cc.game.setFrameRate(15),fgui.addLoadHandler(),fgui.GRoot.create(),fgui.UIPackage.loadPackage("commons",function(){fgui.UIPackage.addPackage("commons"),n.initFire(),n.initDialog(),s.default.getInst().setRoot(e),s.default.getInst().switchLayer(a.default)})},t.initFire=function(){c.default.init(u.default.LOCAL_FIRE),c.default.init(u.default.PLAZA_FIRE),c.default.init(u.default.GAME_FIRE)},t.initDialog=function(){},t=n=o([i],t)}(cc.Component);n.default=f,cc._RF.pop()},{"./AppConfig":"AppConfig","./UIManger":"UIManger","./WelcomeLayer":"WelcomeLayer","./fire/FireKit":"FireKit"}],AiJCCComponent:[function(e,t,n){"use strict";cc._RF.push(t,"156e3BCfORM8ZPUdyCulBbz","AiJCCComponent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("async-lock"),i=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.lock=new o,t.UI_LOCK_KEY="ui_key",t}return r(t,e),t.prototype.loadPackage=function(e,t){this.lock.acquire(this.UI_LOCK_KEY,function(n){fgui.UIPackage.loadPackage(e,function(){t(),n()})},function(){console.log("Load package success!")})},t.prototype.initAiJCom=function(e){var t=this;this.lock.acquire(this.UI_LOCK_KEY,function(n){t.onInitAiJCom(e),n()},function(){console.log("AiJCom init success")})},t}(cc.Component);n.default=i,cc._RF.pop()},{"async-lock":1}],AiJKit:[function(e,t,n){"use strict";cc._RF.push(t,"463e3s0Xb1DoLzbqIFnzE6E","AiJKit"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("./AiJPro"),o=function(){function e(){}return e.init=function(t,n){if(e.exist(t))throw Error("Websocket "+t+" \u5df2\u7ecf\u5b58\u5728");e.aiJProDict[t]=new r.default(n)},e.exist=function(e){return null!=this.aiJProDict[e]},e.use=function(t){return e.aiJProDict[t]},e.close=function(t){e.aiJProDict[t].close(),delete e.aiJProDict[t]},e.aiJProDict={},e}();n.default=o,cc._RF.pop()},{"./AiJPro":"AiJPro"}],AiJPro:[function(e,t,n){"use strict";cc._RF.push(t,"7aa80HptUZONLMv/KLoQsPT","AiJPro"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("./AiJ"),o=function(){function e(e){this.aij=new r.AiJ(e)}return e.prototype.isOpen=function(){return this.aij.aiJWs.readyState==this.aij.aiJWs.ws.OPEN},e.prototype.send=function(e){this.aij.send(e)},e.prototype.connect=function(){this.aij.aiJWs.connect(!1)},e.prototype.close=function(){this.aij.aiJWs.close()},e}();n.default=o,cc._RF.pop()},{"./AiJ":"AiJ"}],AiJ:[function(e,t,n){"use strict";cc._RF.push(t,"5ea82+0RpRAdZXgBDaIywJJ","AiJ"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(t){void 0===this.aiJWs&&(this.aiJWs=null),this.aiJWs=new e.AiJWs(t)}return e.prototype.send=function(e){this.aiJWs.send(e)},e}();n.AiJ=r,r.__class="AiJ",function(e){var t=function(){return function(){void 0===this.mainType&&(this.mainType=0),void 0===this.subType&&(this.subType=0)}}();e.AiJEvent=t,t.__class="AiJ.AiJEvent";var n=function(){return function(){void 0===this.mainType&&(this.mainType=0),void 0===this.subType&&(this.subType=0),void 0===this.code&&(this.code=0),void 0===this.message&&(this.message=null),void 0===this.text&&(this.text=null)}}();e.Response=n,n.__class="AiJ.Response";var r=function(){return function(){}}();e.ResponseHandler=r,r.__class="AiJ.ResponseHandler";var o=function(){return function(){this.connectionTimeout=1e4,this.reconnectInterval=1e3,this.reconnectDecay=1.1,this.maxReconnectInterval=3e4,this.maxRetries=10,this.debug=!0,this.allowReconnect=!0,this.automaticOpen=!0}}();e.Options=o,o.__class="AiJ.Options";var i=function(){function t(n,r){this.mapping=new Object,this.options=new e.Options,this.wsEventListener=new t.Config$0(this),void 0===this.ws&&(this.ws=null),this.ws=n,this.options.connectionTimeout=null!=r.connectionTimeout?r.connectionTimeout:this.options.connectionTimeout,this.options.maxRetries=null!=r.maxRetries?r.maxRetries:this.options.maxRetries,this.options.debug=null!=r.debug?r.debug:this.options.debug,this.options.allowReconnect=null!=r.allowReconnect?r.allowReconnect:this.options.allowReconnect,this.options.automaticOpen=null!=r.automaticOpen?r.automaticOpen:this.options.automaticOpen,this.options.maxReconnectInterval=null!=r.maxReconnectInterval?r.maxReconnectInterval:this.options.maxReconnectInterval,this.options.reconnectInterval=null!=r.reconnectInterval?r.reconnectInterval:this.options.reconnectInterval,this.options.reconnectDecay=null!=r.reconnectDecay?r.reconnectDecay:this.options.reconnectDecay}return t.prototype.setWsEventListener=function(e){this.wsEventListener=e},t.prototype.addRouter=function(e,t,n){var r=this.mapping[new String(e).toString()];null==r&&(r=new Object,this.mapping[new String(e).toString()]=r);var o=r[new String(t).toString()];null==o&&(o=[],r[new String(t).toString()]=o),o.push(n)},t}();e.Config=i,i.__class="AiJ.Config",function(e){var t=function(){function e(e){this.__parent=e}return e.prototype.onConnecting=function(e){e.config.options.debug&&window.console.log("onConnecting")},e.prototype.onOpen=function(e,t,n){e.config.options.debug&&window.console.log("onOpen")},e.prototype.onClose=function(e,t){e.config.options.debug&&window.console.log("onClose")},e.prototype.onForcedClose=function(e,t){e.config.options.debug&&window.console.log("onForcedClose")},e.prototype.onError=function(e,t){e.config.options.debug&&window.console.log("onError")},e.prototype.onMessage=function(e,t){e.config.options.debug&&window.console.log("onMessage")},e.prototype.onTimeout=function(e){e.config.options.debug&&window.console.log("onTimeout")},e.prototype.onReconnectAttempt=function(e,t){e.config.options.debug&&window.console.log("onReconnectAttempt")},e.prototype.onReconnectFail=function(e,t){e.config.options.debug&&window.console.log("onReconnectFail")},e}();e.Config$0=t,t.__interfaces=["AiJ.WsEventListener"]}(i=e.Config||(e.Config={}));var a=function(){function e(e){this.reconnectAttempts=0,this.readyState=-1,this.forcedClose=!1,this.timedOut=!1,void 0===this.self&&(this.self=null),void 0===this.ws&&(this.ws=null),void 0===this.config&&(this.config=null),this.self=this,this.config=e,this.config.options.automaticOpen&&this.connect(!1)}return e.prototype.reconnect=function(){var e=this;if(this.reconnectAttempts++,this.reconnectAttempts>this.config.options.maxRetries)this.config.wsEventListener.onReconnectFail(this,this.reconnectAttempts);else{this.config.wsEventListener.onReconnectAttempt(this,this.reconnectAttempts);var t=this.self.config.options.reconnectInterval*Math.pow(this.self.config.options.reconnectDecay,this.self.reconnectAttempts);window.setTimeout(function(){return e.self.connect(!0)},t>this.self.config.options.maxReconnectInterval?this.self.config.options.maxReconnectInterval:t)}},e.prototype.connect=function(e){var t=this;e||(this.reconnectAttempts=0),this.ws=new WebSocket(this.config.ws),this.forcedClose=!1,this.readyState=this.ws.CONNECTING,this.config.wsEventListener.onConnecting(this.self);var n=window.setTimeout(function(){t.timedOut=!0,t.config.wsEventListener.onTimeout(t.self),t.ws.close(),t.timedOut=!1},this.config.options.connectionTimeout);this.ws.onopen=function(e){return window.clearTimeout(n),t.self.readyState=t.ws.OPEN,t.self.reconnectAttempts=0,t.config.wsEventListener.onOpen(t.self,t.self.reconnectAttempts,e),new Object},this.ws.onclose=function(r){return window.clearTimeout(n),t.forcedClose?(t.self.readyState=t.ws.CLOSED,t.config.wsEventListener.onForcedClose(t.self,r)):(e||t.timedOut||t.config.wsEventListener.onClose(t.self,r),t.reconnect()),new Object},this.ws.onerror=function(e){return window.clearTimeout(n),t.config.wsEventListener.onError(t.self,e),new Object},this.ws.onmessage=function(e){t.config.options.debug&&window.console.log("\u63a5\u6536\u4fe1\u606f:"+JSON.stringify(e.data));try{var n=JSON.parse(e.data);if(null!=n){n.text=e.data;var r=t.config.mapping[new String(n.mainType).toString()];if(null!=r){var o=r[new String(n.subType).toString()];if(null!=o)for(var i=0;i<o.length;i++)o[i].handler(t.self,n);else t.config.options.debug&&window.console.log("mainType:"+n.mainType+" subType:"+n.subType+" no mapping")}else t.config.options.debug&&window.console.log("mainType:"+n.mainType+" no mapping")}}finally{t.config.wsEventListener.onMessage(t.self,e)}return new Object}},e.prototype.send=function(e){this.config.options.debug&&window.console.log("\u53d1\u9001\u4fe1\u606f:"+JSON.stringify(e)),this.ws.send(JSON.stringify(e))},e.prototype.close=function(){this.forcedClose=!0,this.ws.close()},e}();e.AiJWs=a,a.__class="AiJ.AiJWs"}(r=n.AiJ||(n.AiJ={})),n.AiJ=r,cc._RF.pop()},{}],AlertWindow:[function(e,t,n){"use strict";cc._RF.push(t,"139f4lZ/vJKdZWWze0ku7BE","AlertWindow");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return e.call(this)||this}return r(t,e),t.getInst=function(){return null==t.inst&&(t.inst=new t),t.inst},t.alert=function(e,n){var r=t.getInst();r.show(),r.contentPane.getChild("title").asTextField.text=e,r.contentPane.getChild("content").asTextField.text=n},t.prototype.onInit=function(){this.contentPane=fgui.UIPackage.createObject("commons","AlertWindow").asCom,this.center()},t}(fgui.Window);n.default=o,cc._RF.pop()},{}],AppConfig:[function(e,t,n){"use strict";cc._RF.push(t,"12cf41W7tlPzoLZVTcrbgP8","AppConfig"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(){}return e.PLAZA_WS_HOST="localhost",e.PLAZA_WS_PORT=8082,e.PLATFORM_URL="http://localhost:8090/",e.PLAZA_WS_NAME="PLAZA_WS",e.LOCAL_FIRE="LOCAL_FIRE",e.PLAZA_FIRE="PLAZA_FIRE",e.GAME_FIRE="GAME_FIRE",e.GAME_WS_NAME="GAME_WS_NAME",e}();n.default=r,cc._RF.pop()},{}],AudioManager:[function(e,t,n){"use strict";cc._RF.push(t,"1f3aatSwUNFM61nLb0C+xEA","AudioManager"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(){}return e.play_music=function(e,t){var n=fgui.UIPackage.getItemByURL(fgui.UIPackage.getItemURL(e,t));cc.audioEngine.playMusic(n.owner.getItemAsset(n),!0)},e.play_effect=function(e,t){var n=fgui.UIPackage.getItemByURL(fgui.UIPackage.getItemURL(e,t));cc.audioEngine.playEffect(n.owner.getItemAsset(n),!0)},e.stop_music=function(){cc.audioEngine.stopMusic()},e}();n.default=r,cc._RF.pop()},{}],BroadcastEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"975ccaM1Z1LyqRUh3utBqKl","BroadcastEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).emit("broadcast",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],BroadcastEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"5146dXpdVZIHYqmg98/wgoI","BroadcastEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.broadcasts=[],t}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],BroadcastEvent:[function(e,t,n){"use strict";cc._RF.push(t,"0ffd3PxugRGGYpAK+d8lgM6","BroadcastEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.mainType=2,t.subType=2,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],ChatEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"a53a7mCafxNb7S79qSuo3kS","ChatEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){},t}(e("../../ws/AiJ").AiJ.ResponseHandler);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],ClientReadyEvent:[function(e,t,n){"use strict";cc._RF.push(t,"41539WwHgRIn4zyS9TZx6oU","ClientReadyEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.mainType=2,t.subType=5,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],CreateTableEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"96f7a7y86JNSpxX6OkM5bdk","CreateTableEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).emit("create_table_success",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],CreateTableEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"0ff8cwDxFNMSLp6GUqjnra9","CreateTableEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],CreateTableEvent:[function(e,t,n){"use strict";cc._RF.push(t,"28eddJWtRZHF5UW+YtHPijP","CreateTableEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.ruleText="{}",t.mainType=2,t.subType=1,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],1:[function(e,t,n){"use strict";t.exports=e("./lib")},{"./lib":2}],2:[function(e,t,n){(function(e){"use strict";var n=function(e){e=e||{},this.Promise=e.Promise||Promise,this.queues={},this.domains={},this.domainReentrant=e.domainReentrant||!1,this.timeout=e.timeout||n.DEFAULT_TIMEOUT,this.maxPending=e.maxPending||n.DEFAULT_MAX_PENDING};n.DEFAULT_TIMEOUT=0,n.DEFAULT_MAX_PENDING=1e3,n.prototype.acquire=function(t,n,r,o){if(Array.isArray(t))return this._acquireBatch(t,n,r,o);if("function"!=typeof n)throw new Error("You must pass a function to execute");var i=null;"function"!=typeof r&&(o=r,r=null,i=this._deferPromise()),o=o||{};var a=!1,s=null,u=this,c=function(e,n,o){e&&(0===u.queues[t].length&&delete u.queues[t],delete u.domains[t]),a||(i?n?i.reject(n):i.resolve(o):"function"==typeof r&&r(n,o),a=!0),e&&u.queues[t]&&u.queues[t].length>0&&u.queues[t].shift()()},f=function(r){if(a)return c(r);if(s&&(clearTimeout(s),s=null),r&&(u.domains[t]=e.domain),1===n.length){var o=!1;n(function(e,t){o||(o=!0,c(r,e,t))})}else u._promiseTry(function(){return n()}).then(function(e){c(r,void 0,e)},function(e){c(r,e)})};if(e.domain&&(f=e.domain.bind(f)),u.queues[t])if(u.domainReentrant&&e.domain&&e.domain===u.domains[t])f(!1);else if(u.queues[t].length>=u.maxPending)c(!1,new Error("Too much pending tasks"));else{var l=function(){f(!0)};o.skipQueue?u.queues[t].unshift(l):u.queues[t].push(l);var p=o.timeout||u.timeout;p&&(s=setTimeout(function(){s=null,c(!1,new Error("async-lock timed out"))},p))}else u.queues[t]=[],f(!0);return i?i.promise:void 0},n.prototype._acquireBatch=function(e,t,n,r){"function"!=typeof n&&(r=n,n=null);var o=this,i=function(e,t){return function(n){o.acquire(e,t,n,r)}},a=t;if(e.reverse().forEach(function(e){a=i(e,a)}),"function"!=typeof n){var s=this._deferPromise();return 1===a.length?a(function(e,t){e?s.reject(e):s.resolve(t)}):s.resolve(a()),s.promise}a(n)},n.prototype.isBusy=function(e){return e?!!this.queues[e]:Object.keys(this.queues).length>0},n.prototype._promiseTry=function(e){try{return this.Promise.resolve(e())}catch(e){return this.Promise.reject(e)}},n.prototype._deferPromise=function(){if("function"==typeof this.Promise.defer)return this.Promise.defer();var e={reject:function(t){return Promise.resolve().then(function(){e.reject(t)})},resolve:function(t){return Promise.resolve().then(function(){e.resolve(t)})},promise:void 0};return e.promise=new this.Promise(function(t,n){e.reject=n,e.resolve=t}),e},t.exports=n}).call(this,e("_process"))},{_process:8}],3:[function(e,t,n){var r={utf8:{stringToBytes:function(e){return r.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(r.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}};t.exports=r},{}],4:[function(e,t,n){(function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(t){for(var n=[],r=0;r<t.length;r+=3)for(var o=t[r]<<16|t[r+1]<<8|t[r+2],i=0;i<4;i++)8*r+6*i<=8*t.length?n.push(e.charAt(o>>>6*(3-i)&63)):n.push("=");return n.join("")},base64ToBytes:function(t){t=t.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,o=0;r<t.length;o=++r%4)0!=o&&n.push((e.indexOf(t.charAt(r-1))&Math.pow(2,-2*o+8)-1)<<2*o|e.indexOf(t.charAt(r))>>>6-2*o);return n}};t.exports=n})()},{}],5:[function(e,t,n){function r(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}function o(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&r(e.slice(0,0))}t.exports=function(e){return null!=e&&(r(e)||o(e)||!!e._isBuffer)}},{}],6:[function(e,t,n){(function(e){(function(){var r,o=200,i="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",a="Expected a function",s="__lodash_hash_undefined__",u=500,c="__lodash_placeholder__",f=1,l=2,p=4,h=1,d=2,_=1,g=2,v=4,y=8,m=16,w=32,C=64,R=128,A=256,b=512,O=30,j="...",P=800,E=16,I=1,M=2,F=1/0,L=9007199254740991,J=1.7976931348623157e308,T=NaN,x=4294967295,S=x-1,H=x>>>1,U=[["ary",R],["bind",_],["bindKey",g],["curry",y],["curryRight",m],["flip",b],["partial",w],["partialRight",C],["rearg",A]],k="[object Arguments]",G="[object Array]",V="[object AsyncFunction]",W="[object Boolean]",N="[object Date]",D="[object DOMException]",B="[object Error]",K="[object Function]",z="[object GeneratorFunction]",Z="[object Map]",q="[object Number]",Y="[object Null]",X="[object Object]",$="[object Proxy]",Q="[object RegExp]",ee="[object Set]",te="[object String]",ne="[object Symbol]",re="[object Undefined]",oe="[object WeakMap]",ie="[object WeakSet]",ae="[object ArrayBuffer]",se="[object DataView]",ue="[object Float32Array]",ce="[object Float64Array]",fe="[object Int8Array]",le="[object Int16Array]",pe="[object Int32Array]",he="[object Uint8Array]",de="[object Uint8ClampedArray]",_e="[object Uint16Array]",ge="[object Uint32Array]",ve=/\b__p \+= '';/g,ye=/\b(__p \+=) '' \+/g,me=/(__e\(.*?\)|\b__t\)) \+\n'';/g,we=/&(?:amp|lt|gt|quot|#39);/g,Ce=/[&<>"']/g,Re=RegExp(we.source),Ae=RegExp(Ce.source),be=/<%-([\s\S]+?)%>/g,Oe=/<%([\s\S]+?)%>/g,je=/<%=([\s\S]+?)%>/g,Pe=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Ee=/^\w*$/,Ie=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Me=/[\\^$.*+?()[\]{}|]/g,Fe=RegExp(Me.source),Le=/^\s+|\s+$/g,Je=/^\s+/,Te=/\s+$/,xe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Se=/\{\n\/\* \[wrapped with (.+)\] \*/,He=/,? & /,Ue=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,ke=/\\(\\)?/g,Ge=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Ve=/\w*$/,We=/^[-+]0x[0-9a-f]+$/i,Ne=/^0b[01]+$/i,De=/^\[object .+?Constructor\]$/,Be=/^0o[0-7]+$/i,Ke=/^(?:0|[1-9]\d*)$/,ze=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Ze=/($^)/,qe=/['\n\r\u2028\u2029\\]/g,Ye="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Xe="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",$e="[\\ud800-\\udfff]",Qe="["+Xe+"]",et="["+Ye+"]",tt="\\d+",nt="[\\u2700-\\u27bf]",rt="[a-z\\xdf-\\xf6\\xf8-\\xff]",ot="[^\\ud800-\\udfff"+Xe+tt+"\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",it="\\ud83c[\\udffb-\\udfff]",at="[^\\ud800-\\udfff]",st="(?:\\ud83c[\\udde6-\\uddff]){2}",ut="[\\ud800-\\udbff][\\udc00-\\udfff]",ct="[A-Z\\xc0-\\xd6\\xd8-\\xde]",ft="(?:"+rt+"|"+ot+")",lt="(?:"+ct+"|"+ot+")",pt="(?:"+et+"|"+it+")"+"?",ht="[\\ufe0e\\ufe0f]?"+pt+("(?:\\u200d(?:"+[at,st,ut].join("|")+")[\\ufe0e\\ufe0f]?"+pt+")*"),dt="(?:"+[nt,st,ut].join("|")+")"+ht,_t="(?:"+[at+et+"?",et,st,ut,$e].join("|")+")",gt=RegExp("['\u2019]","g"),vt=RegExp(et,"g"),yt=RegExp(it+"(?="+it+")|"+_t+ht,"g"),mt=RegExp([ct+"?"+rt+"+(?:['\u2019](?:d|ll|m|re|s|t|ve))?(?="+[Qe,ct,"$"].join("|")+")",lt+"+(?:['\u2019](?:D|LL|M|RE|S|T|VE))?(?="+[Qe,ct+ft,"$"].join("|")+")",ct+"?"+ft+"+(?:['\u2019](?:d|ll|m|re|s|t|ve))?",ct+"+(?:['\u2019](?:D|LL|M|RE|S|T|VE))?","\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",tt,dt].join("|"),"g"),wt=RegExp("[\\u200d\\ud800-\\udfff"+Ye+"\\ufe0e\\ufe0f]"),Ct=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Rt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],At=-1,bt={};bt[ue]=bt[ce]=bt[fe]=bt[le]=bt[pe]=bt[he]=bt[de]=bt[_e]=bt[ge]=!0,bt[k]=bt[G]=bt[ae]=bt[W]=bt[se]=bt[N]=bt[B]=bt[K]=bt[Z]=bt[q]=bt[X]=bt[Q]=bt[ee]=bt[te]=bt[oe]=!1;var Ot={};Ot[k]=Ot[G]=Ot[ae]=Ot[se]=Ot[W]=Ot[N]=Ot[ue]=Ot[ce]=Ot[fe]=Ot[le]=Ot[pe]=Ot[Z]=Ot[q]=Ot[X]=Ot[Q]=Ot[ee]=Ot[te]=Ot[ne]=Ot[he]=Ot[de]=Ot[_e]=Ot[ge]=!0,Ot[B]=Ot[K]=Ot[oe]=!1;var jt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Pt=parseFloat,Et=parseInt,It="object"==typeof e&&e&&e.Object===Object&&e,Mt="object"==typeof self&&self&&self.Object===Object&&self,Ft=It||Mt||Function("return this")(),Lt="object"==typeof n&&n&&!n.nodeType&&n,Jt=Lt&&"object"==typeof t&&t&&!t.nodeType&&t,Tt=Jt&&Jt.exports===Lt,xt=Tt&&It.process,St=function(){try{var e=Jt&&Jt.require&&Jt.require("util").types;return e||xt&&xt.binding&&xt.binding("util")}catch(e){}}(),Ht=St&&St.isArrayBuffer,Ut=St&&St.isDate,kt=St&&St.isMap,Gt=St&&St.isRegExp,Vt=St&&St.isSet,Wt=St&&St.isTypedArray;function Nt(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function Dt(e,t,n,r){for(var o=-1,i=null==e?0:e.length;++o<i;){var a=e[o];t(r,a,n(a),e)}return r}function Bt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e}function Kt(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function zt(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0}function Zt(e,t){for(var n=-1,r=null==e?0:e.length,o=0,i=[];++n<r;){var a=e[n];t(a,n,e)&&(i[o++]=a)}return i}function qt(e,t){return!!(null==e?0:e.length)&&un(e,t,0)>-1}function Yt(e,t,n){for(var r=-1,o=null==e?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}function Xt(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}function $t(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}function Qt(e,t,n,r){var o=-1,i=null==e?0:e.length;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}function en(e,t,n,r){var o=null==e?0:e.length;for(r&&o&&(n=e[--o]);o--;)n=t(n,e[o],o,e);return n}function tn(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}var nn=pn("length");function rn(e){return e.split("")}function on(e){return e.match(Ue)||[]}function an(e,t,n){var r;return n(e,function(e,n,o){if(t(e,n,o))return r=n,!1}),r}function sn(e,t,n,r){for(var o=e.length,i=n+(r?1:-1);r?i--:++i<o;)if(t(e[i],i,e))return i;return-1}function un(e,t,n){return t==t?Hn(e,t,n):sn(e,fn,n)}function cn(e,t,n,r){for(var o=n-1,i=e.length;++o<i;)if(r(e[o],t))return o;return-1}function fn(e){return e!=e}function ln(e,t){var n=null==e?0:e.length;return n?gn(e,t)/n:T}function pn(e){return function(t){return null==t?r:t[e]}}function hn(e){return function(t){return null==e?r:e[t]}}function dn(e,t,n,r,o){return o(e,function(e,o,i){n=r?(r=!1,e):t(n,e,o,i)}),n}function _n(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}function gn(e,t){for(var n,o=-1,i=e.length;++o<i;){var a=t(e[o]);a!==r&&(n=n===r?a:n+a)}return n}function vn(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}function yn(e,t){return Xt(t,function(t){return[t,e[t]]})}function mn(e){return function(t){return e(t)}}function wn(e,t){return Xt(t,function(t){return e[t]})}function Cn(e,t){return e.has(t)}function Rn(e,t){for(var n=-1,r=e.length;++n<r&&un(t,e[n],0)>-1;);return n}function An(e,t){for(var n=e.length;n--&&un(t,e[n],0)>-1;);return n}function bn(e,t){for(var n=e.length,r=0;n--;)e[n]===t&&++r;return r}var On=hn({"\xc0":"A","\xc1":"A","\xc2":"A","\xc3":"A","\xc4":"A","\xc5":"A","\xe0":"a","\xe1":"a","\xe2":"a","\xe3":"a","\xe4":"a","\xe5":"a","\xc7":"C","\xe7":"c","\xd0":"D","\xf0":"d","\xc8":"E","\xc9":"E","\xca":"E","\xcb":"E","\xe8":"e","\xe9":"e","\xea":"e","\xeb":"e","\xcc":"I","\xcd":"I","\xce":"I","\xcf":"I","\xec":"i","\xed":"i","\xee":"i","\xef":"i","\xd1":"N","\xf1":"n","\xd2":"O","\xd3":"O","\xd4":"O","\xd5":"O","\xd6":"O","\xd8":"O","\xf2":"o","\xf3":"o","\xf4":"o","\xf5":"o","\xf6":"o","\xf8":"o","\xd9":"U","\xda":"U","\xdb":"U","\xdc":"U","\xf9":"u","\xfa":"u","\xfb":"u","\xfc":"u","\xdd":"Y","\xfd":"y","\xff":"y","\xc6":"Ae","\xe6":"ae","\xde":"Th","\xfe":"th","\xdf":"ss","\u0100":"A","\u0102":"A","\u0104":"A","\u0101":"a","\u0103":"a","\u0105":"a","\u0106":"C","\u0108":"C","\u010a":"C","\u010c":"C","\u0107":"c","\u0109":"c","\u010b":"c","\u010d":"c","\u010e":"D","\u0110":"D","\u010f":"d","\u0111":"d","\u0112":"E","\u0114":"E","\u0116":"E","\u0118":"E","\u011a":"E","\u0113":"e","\u0115":"e","\u0117":"e","\u0119":"e","\u011b":"e","\u011c":"G","\u011e":"G","\u0120":"G","\u0122":"G","\u011d":"g","\u011f":"g","\u0121":"g","\u0123":"g","\u0124":"H","\u0126":"H","\u0125":"h","\u0127":"h","\u0128":"I","\u012a":"I","\u012c":"I","\u012e":"I","\u0130":"I","\u0129":"i","\u012b":"i","\u012d":"i","\u012f":"i","\u0131":"i","\u0134":"J","\u0135":"j","\u0136":"K","\u0137":"k","\u0138":"k","\u0139":"L","\u013b":"L","\u013d":"L","\u013f":"L","\u0141":"L","\u013a":"l","\u013c":"l","\u013e":"l","\u0140":"l","\u0142":"l","\u0143":"N","\u0145":"N","\u0147":"N","\u014a":"N","\u0144":"n","\u0146":"n","\u0148":"n","\u014b":"n","\u014c":"O","\u014e":"O","\u0150":"O","\u014d":"o","\u014f":"o","\u0151":"o","\u0154":"R","\u0156":"R","\u0158":"R","\u0155":"r","\u0157":"r","\u0159":"r","\u015a":"S","\u015c":"S","\u015e":"S","\u0160":"S","\u015b":"s","\u015d":"s","\u015f":"s","\u0161":"s","\u0162":"T","\u0164":"T","\u0166":"T","\u0163":"t","\u0165":"t","\u0167":"t","\u0168":"U","\u016a":"U","\u016c":"U","\u016e":"U","\u0170":"U","\u0172":"U","\u0169":"u","\u016b":"u","\u016d":"u","\u016f":"u","\u0171":"u","\u0173":"u","\u0174":"W","\u0175":"w","\u0176":"Y","\u0177":"y","\u0178":"Y","\u0179":"Z","\u017b":"Z","\u017d":"Z","\u017a":"z","\u017c":"z","\u017e":"z","\u0132":"IJ","\u0133":"ij","\u0152":"Oe","\u0153":"oe","\u0149":"'n","\u017f":"s"}),jn=hn({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function Pn(e){return"\\"+jt[e]}function En(e,t){return null==e?r:e[t]}function In(e){return wt.test(e)}function Mn(e){return Ct.test(e)}function Fn(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}function Ln(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n}function Jn(e,t){return function(n){return e(t(n))}}function Tn(e,t){for(var n=-1,r=e.length,o=0,i=[];++n<r;){var a=e[n];a!==t&&a!==c||(e[n]=c,i[o++]=n)}return i}function xn(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}function Sn(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=[e,e]}),n}function Hn(e,t,n){for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}function Un(e,t,n){for(var r=n+1;r--;)if(e[r]===t)return r;return r}function kn(e){return In(e)?Wn(e):nn(e)}function Gn(e){return In(e)?Nn(e):rn(e)}var Vn=hn({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});function Wn(e){for(var t=yt.lastIndex=0;yt.test(e);)++t;return t}function Nn(e){return e.match(yt)||[]}function Dn(e){return e.match(mt)||[]}var Bn=function e(t){var n=(t=null==t?Ft:Bn.defaults(Ft.Object(),t,Bn.pick(Ft,Rt))).Array,Ue=t.Date,Ye=t.Error,Xe=t.Function,$e=t.Math,Qe=t.Object,et=t.RegExp,tt=t.String,nt=t.TypeError,rt=n.prototype,ot=Xe.prototype,it=Qe.prototype,at=t["__core-js_shared__"],st=ot.toString,ut=it.hasOwnProperty,ct=0,ft=function(){var e=/[^.]+$/.exec(at&&at.keys&&at.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),lt=it.toString,pt=st.call(Qe),ht=Ft._,dt=et("^"+st.call(ut).replace(Me,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),_t=Tt?t.Buffer:r,yt=t.Symbol,mt=t.Uint8Array,wt=_t?_t.allocUnsafe:r,Ct=Jn(Qe.getPrototypeOf,Qe),jt=Qe.create,It=it.propertyIsEnumerable,Mt=rt.splice,Lt=yt?yt.isConcatSpreadable:r,Jt=yt?yt.iterator:r,xt=yt?yt.toStringTag:r,St=function(){try{var e=la(Qe,"defineProperty");return e({},"",{}),e}catch(e){}}(),nn=t.clearTimeout!==Ft.clearTimeout&&t.clearTimeout,rn=Ue&&Ue.now!==Ft.Date.now&&Ue.now,hn=t.setTimeout!==Ft.setTimeout&&t.setTimeout,Hn=$e.ceil,Wn=$e.floor,Nn=Qe.getOwnPropertySymbols,Kn=_t?_t.isBuffer:r,zn=t.isFinite,Zn=rt.join,qn=Jn(Qe.keys,Qe),Yn=$e.max,Xn=$e.min,$n=Ue.now,Qn=t.parseInt,er=$e.random,tr=rt.reverse,nr=la(t,"DataView"),rr=la(t,"Map"),or=la(t,"Promise"),ir=la(t,"Set"),ar=la(t,"WeakMap"),sr=la(Qe,"create"),ur=ar&&new ar,cr={},fr=qa(nr),lr=qa(rr),pr=qa(or),hr=qa(ir),dr=qa(ar),_r=yt?yt.prototype:r,gr=_r?_r.valueOf:r,vr=_r?_r.toString:r;function yr(e){if(hu(e)&&!nu(e)&&!(e instanceof Rr)){if(e instanceof Cr)return e;if(ut.call(e,"__wrapped__"))return Xa(e)}return new Cr(e)}var mr=function(){function e(){}return function(t){if(!pu(t))return{};if(jt)return jt(t);e.prototype=t;var n=new e;return e.prototype=r,n}}();function wr(){}function Cr(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=r}function Rr(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=x,this.__views__=[]}function Ar(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function br(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Or(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function jr(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Or;++t<n;)this.add(e[t])}function Pr(e){var t=this.__data__=new br(e);this.size=t.size}function Er(e,t){var n=nu(e),r=!n&&tu(e),o=!n&&!r&&au(e),i=!n&&!r&&!o&&Cu(e),a=n||r||o||i,s=a?vn(e.length,tt):[],u=s.length;for(var c in e)!t&&!ut.call(e,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||ba(c,u))||s.push(c);return s}function Ir(e){var t=e.length;return t?e[So(0,t-1)]:r}function Mr(e,t){return Ka(Ri(e),Gr(t,0,e.length))}function Fr(e){return Ka(Ri(e))}function Lr(e,t,n){(n===r||$s(e[t],n))&&(n!==r||t in e)||Ur(e,t,n)}function Jr(e,t,n){var o=e[t];ut.call(e,t)&&$s(o,n)&&(n!==r||t in e)||Ur(e,t,n)}function Tr(e,t){for(var n=e.length;n--;)if($s(e[n][0],t))return n;return-1}function xr(e,t,n,r){return Kr(e,function(e,o,i){t(r,e,n(e),i)}),r}function Sr(e,t){return e&&Ai(t,Nu(t),e)}function Hr(e,t){return e&&Ai(t,Du(t),e)}function Ur(e,t,n){"__proto__"==t&&St?St(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function kr(e,t){for(var o=-1,i=t.length,a=n(i),s=null==e;++o<i;)a[o]=s?r:Uu(e,t[o]);return a}function Gr(e,t,n){return e==e&&(n!==r&&(e=e<=n?e:n),t!==r&&(e=e>=t?e:t)),e}function Vr(e,t,n,o,i,a){var s,u=t&f,c=t&l,h=t&p;if(n&&(s=i?n(e,o,i,a):n(e)),s!==r)return s;if(!pu(e))return e;var d=nu(e);if(d){if(s=ma(e),!u)return Ri(e,s)}else{var _=_a(e),g=_==K||_==z;if(au(e))return pi(e,u);if(_==X||_==k||g&&!i){if(s=c||g?{}:wa(e),!u)return c?Oi(e,Hr(s,e)):bi(e,Sr(s,e))}else{if(!Ot[_])return i?e:{};s=Ca(e,_,u)}}a||(a=new Pr);var v=a.get(e);if(v)return v;if(a.set(e,s),yu(e))return e.forEach(function(r){s.add(Vr(r,t,n,r,e,a))}),s;if(du(e))return e.forEach(function(r,o){s.set(o,Vr(r,t,n,o,e,a))}),s;var y=d?r:(h?c?oa:ra:c?Du:Nu)(e);return Bt(y||e,function(r,o){y&&(r=e[o=r]),Jr(s,o,Vr(r,t,n,o,e,a))}),s}function Wr(e){var t=Nu(e);return function(n){return Nr(n,e,t)}}function Nr(e,t,n){var o=n.length;if(null==e)return!o;for(e=Qe(e);o--;){var i=n[o],a=t[i],s=e[i];if(s===r&&!(i in e)||!a(s))return!1}return!0}function Dr(e,t,n){if("function"!=typeof e)throw new nt(a);return Wa(function(){e.apply(r,n)},t)}function Br(e,t,n,r){var i=-1,a=qt,s=!0,u=e.length,c=[],f=t.length;if(!u)return c;n&&(t=Xt(t,mn(n))),r?(a=Yt,s=!1):t.length>=o&&(a=Cn,s=!1,t=new jr(t));e:for(;++i<u;){var l=e[i],p=null==n?l:n(l);if(l=r||0!==l?l:0,s&&p==p){for(var h=f;h--;)if(t[h]===p)continue e;c.push(l)}else a(t,p,r)||c.push(l)}return c}yr.templateSettings={escape:be,evaluate:Oe,interpolate:je,variable:"",imports:{_:yr}},yr.prototype=wr.prototype,yr.prototype.constructor=yr,Cr.prototype=mr(wr.prototype),Cr.prototype.constructor=Cr,Rr.prototype=mr(wr.prototype),Rr.prototype.constructor=Rr,Ar.prototype.clear=function(){this.__data__=sr?sr(null):{},this.size=0},Ar.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Ar.prototype.get=function(e){var t=this.__data__;if(sr){var n=t[e];return n===s?r:n}return ut.call(t,e)?t[e]:r},Ar.prototype.has=function(e){var t=this.__data__;return sr?t[e]!==r:ut.call(t,e)},Ar.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=sr&&t===r?s:t,this},br.prototype.clear=function(){this.__data__=[],this.size=0},br.prototype.delete=function(e){var t=this.__data__,n=Tr(t,e);return!(n<0||(n==t.length-1?t.pop():Mt.call(t,n,1),--this.size,0))},br.prototype.get=function(e){var t=this.__data__,n=Tr(t,e);return n<0?r:t[n][1]},br.prototype.has=function(e){return Tr(this.__data__,e)>-1},br.prototype.set=function(e,t){var n=this.__data__,r=Tr(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},Or.prototype.clear=function(){this.size=0,this.__data__={hash:new Ar,map:new(rr||br),string:new Ar}},Or.prototype.delete=function(e){var t=ca(this,e).delete(e);return this.size-=t?1:0,t},Or.prototype.get=function(e){return ca(this,e).get(e)},Or.prototype.has=function(e){return ca(this,e).has(e)},Or.prototype.set=function(e,t){var n=ca(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},jr.prototype.add=jr.prototype.push=function(e){return this.__data__.set(e,s),this},jr.prototype.has=function(e){return this.__data__.has(e)},Pr.prototype.clear=function(){this.__data__=new br,this.size=0},Pr.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Pr.prototype.get=function(e){return this.__data__.get(e)},Pr.prototype.has=function(e){return this.__data__.has(e)},Pr.prototype.set=function(e,t){var n=this.__data__;if(n instanceof br){var r=n.__data__;if(!rr||r.length<o-1)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new Or(r)}return n.set(e,t),this.size=n.size,this};var Kr=Ei(to),zr=Ei(no,!0);function Zr(e,t){var n=!0;return Kr(e,function(e,r,o){return n=!!t(e,r,o)}),n}function qr(e,t,n){for(var o=-1,i=e.length;++o<i;){var a=e[o],s=t(a);if(null!=s&&(u===r?s==s&&!wu(s):n(s,u)))var u=s,c=a}return c}function Yr(e,t,n,o){var i=e.length;for((n=ju(n))<0&&(n=-n>i?0:i+n),(o=o===r||o>i?i:ju(o))<0&&(o+=i),o=n>o?0:Pu(o);n<o;)e[n++]=t;return e}function Xr(e,t){var n=[];return Kr(e,function(e,r,o){t(e,r,o)&&n.push(e)}),n}function $r(e,t,n,r,o){var i=-1,a=e.length;for(n||(n=Aa),o||(o=[]);++i<a;){var s=e[i];t>0&&n(s)?t>1?$r(s,t-1,n,r,o):$t(o,s):r||(o[o.length]=s)}return o}var Qr=Ii(),eo=Ii(!0);function to(e,t){return e&&Qr(e,t,Nu)}function no(e,t){return e&&eo(e,t,Nu)}function ro(e,t){return Zt(t,function(t){return cu(e[t])})}function oo(e,t){for(var n=0,o=(t=ui(t,e)).length;null!=e&&n<o;)e=e[Za(t[n++])];return n&&n==o?e:r}function io(e,t,n){var r=t(e);return nu(e)?r:$t(r,n(e))}function ao(e){return null==e?e===r?re:Y:xt&&xt in Qe(e)?pa(e):Sa(e)}function so(e,t){return e>t}function uo(e,t){return null!=e&&ut.call(e,t)}function co(e,t){return null!=e&&t in Qe(e)}function fo(e,t,n){return e>=Xn(t,n)&&e<Yn(t,n)}function lo(e,t,o){for(var i=o?Yt:qt,a=e[0].length,s=e.length,u=s,c=n(s),f=1/0,l=[];u--;){var p=e[u];u&&t&&(p=Xt(p,mn(t))),f=Xn(p.length,f),c[u]=!o&&(t||a>=120&&p.length>=120)?new jr(u&&p):r}p=e[0];var h=-1,d=c[0];e:for(;++h<a&&l.length<f;){var _=p[h],g=t?t(_):_;if(_=o||0!==_?_:0,!(d?Cn(d,g):i(l,g,o))){for(u=s;--u;){var v=c[u];if(!(v?Cn(v,g):i(e[u],g,o)))continue e}d&&d.push(g),l.push(_)}}return l}function po(e,t,n,r){return to(e,function(e,o,i){t(r,n(e),o,i)}),r}function ho(e,t,n){var o=null==(e=Ua(e,t=ui(t,e)))?e:e[Za(us(t))];return null==o?r:Nt(o,e,n)}function _o(e){return hu(e)&&ao(e)==k}function go(e,t,n,r,o){return e===t||(null==e||null==t||!hu(e)&&!hu(t)?e!=e&&t!=t:vo(e,t,n,r,go,o))}function vo(e,t,n,r,o,i){var a=nu(e),s=nu(t),u=a?G:_a(e),c=s?G:_a(t),f=(u=u==k?X:u)==X,l=(c=c==k?X:c)==X,p=u==c;if(p&&au(e)){if(!au(t))return!1;a=!0,f=!1}if(p&&!f)return i||(i=new Pr),a||Cu(e)?Qi(e,t,n,r,o,i):ea(e,t,u,n,r,o,i);if(!(n&h)){var d=f&&ut.call(e,"__wrapped__"),_=l&&ut.call(t,"__wrapped__");if(d||_){var g=d?e.value():e,v=_?t.value():t;return i||(i=new Pr),o(g,v,n,r,i)}}return!!p&&(i||(i=new Pr),ta(e,t,n,r,o,i))}function yo(e,t,n,o){var i=n.length,a=i,s=!o;if(null==e)return!a;for(e=Qe(e);i--;){var u=n[i];if(s&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++i<a;){var c=(u=n[i])[0],f=e[c],l=u[1];if(s&&u[2]){if(f===r&&!(c in e))return!1}else{var p=new Pr;if(o)var _=o(f,l,c,e,t,p);if(!(_===r?go(l,f,h|d,o,p):_))return!1}}return!0}function mo(e){return!(!pu(e)||Ia(e))&&(cu(e)?dt:De).test(qa(e))}function wo(e){return"function"==typeof e?e:null==e?_c:"object"==typeof e?nu(e)?jo(e[0],e[1]):Oo(e):bc(e)}function Co(e){if(!Fa(e))return qn(e);var t=[];for(var n in Qe(e))ut.call(e,n)&&"constructor"!=n&&t.push(n);return t}function Ro(e){if(!pu(e))return xa(e);var t=Fa(e),n=[];for(var r in e)("constructor"!=r||!t&&ut.call(e,r))&&n.push(r);return n}function Ao(e,t){return e<t}function bo(e,t){var r=-1,o=ou(e)?n(e.length):[];return Kr(e,function(e,n,i){o[++r]=t(e,n,i)}),o}function Oo(e){var t=fa(e);return 1==t.length&&t[0][2]?Ja(t[0][0],t[0][1]):function(n){return n===e||yo(n,e,t)}}function jo(e,t){return ja(e)&&La(t)?Ja(Za(e),t):function(n){var o=Uu(n,e);return o===r&&o===t?ku(n,e):go(t,o,h|d)}}function Po(e,t,n,o,i){e!==t&&Qr(t,function(a,s){if(pu(a))i||(i=new Pr),Eo(e,t,s,n,Po,o,i);else{var u=o?o(Ga(e,s),a,s+"",e,t,i):r;u===r&&(u=a),Lr(e,s,u)}},Du)}function Eo(e,t,n,o,i,a,s){var u=Ga(e,n),c=Ga(t,n),f=s.get(c);if(f)Lr(e,n,f);else{var l=a?a(u,c,n+"",e,t,s):r,p=l===r;if(p){var h=nu(c),d=!h&&au(c),_=!h&&!d&&Cu(c);l=c,h||d||_?nu(u)?l=u:iu(u)?l=Ri(u):d?(p=!1,l=pi(c,!0)):_?(p=!1,l=vi(c,!0)):l=[]:gu(c)||tu(c)?(l=u,tu(u)?l=Iu(u):pu(u)&&!cu(u)||(l=wa(c))):p=!1}p&&(s.set(c,l),i(l,c,o,a,s),s.delete(c)),Lr(e,n,l)}}function Io(e,t){var n=e.length;if(n)return ba(t+=t<0?n:0,n)?e[t]:r}function Mo(e,t,n){var r=-1;return t=Xt(t.length?t:[_c],mn(ua())),_n(bo(e,function(e,n,o){return{criteria:Xt(t,function(t){return t(e)}),index:++r,value:e}}),function(e,t){return mi(e,t,n)})}function Fo(e,t){return Lo(e,t,function(t,n){return ku(e,n)})}function Lo(e,t,n){for(var r=-1,o=t.length,i={};++r<o;){var a=t[r],s=oo(e,a);n(s,a)&&Wo(i,ui(a,e),s)}return i}function Jo(e){return function(t){return oo(t,e)}}function To(e,t,n,r){var o=r?cn:un,i=-1,a=t.length,s=e;for(e===t&&(t=Ri(t)),n&&(s=Xt(e,mn(n)));++i<a;)for(var u=0,c=t[i],f=n?n(c):c;(u=o(s,f,u,r))>-1;)s!==e&&Mt.call(s,u,1),Mt.call(e,u,1);return e}function xo(e,t){for(var n=e?t.length:0,r=n-1;n--;){var o=t[n];if(n==r||o!==i){var i=o;ba(o)?Mt.call(e,o,1):ei(e,o)}}return e}function So(e,t){return e+Wn(er()*(t-e+1))}function Ho(e,t,r,o){for(var i=-1,a=Yn(Hn((t-e)/(r||1)),0),s=n(a);a--;)s[o?a:++i]=e,e+=r;return s}function Uo(e,t){var n="";if(!e||t<1||t>L)return n;do{t%2&&(n+=e),(t=Wn(t/2))&&(e+=e)}while(t);return n}function ko(e,t){return Na(Ha(e,t,_c),e+"")}function Go(e){return Ir($u(e))}function Vo(e,t){var n=$u(e);return Ka(n,Gr(t,0,n.length))}function Wo(e,t,n,o){if(!pu(e))return e;for(var i=-1,a=(t=ui(t,e)).length,s=a-1,u=e;null!=u&&++i<a;){var c=Za(t[i]),f=n;if(i!=s){var l=u[c];(f=o?o(l,c,u):r)===r&&(f=pu(l)?l:ba(t[i+1])?[]:{})}Jr(u,c,f),u=u[c]}return e}var No=ur?function(e,t){return ur.set(e,t),e}:_c,Do=St?function(e,t){return St(e,"toString",{configurable:!0,enumerable:!1,value:pc(t),writable:!0})}:_c;function Bo(e){return Ka($u(e))}function Ko(e,t,r){var o=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=n(i);++o<i;)a[o]=e[o+t];return a}function zo(e,t){var n;return Kr(e,function(e,r,o){return!(n=t(e,r,o))}),!!n}function Zo(e,t,n){var r=0,o=null==e?r:e.length;if("number"==typeof t&&t==t&&o<=H){for(;r<o;){var i=r+o>>>1,a=e[i];null!==a&&!wu(a)&&(n?a<=t:a<t)?r=i+1:o=i}return o}return qo(e,t,_c,n)}function qo(e,t,n,o){t=n(t);for(var i=0,a=null==e?0:e.length,s=t!=t,u=null===t,c=wu(t),f=t===r;i<a;){var l=Wn((i+a)/2),p=n(e[l]),h=p!==r,d=null===p,_=p==p,g=wu(p);if(s)var v=o||_;else v=f?_&&(o||h):u?_&&h&&(o||!d):c?_&&h&&!d&&(o||!g):!d&&!g&&(o?p<=t:p<t);v?i=l+1:a=l}return Xn(a,S)}function Yo(e,t){for(var n=-1,r=e.length,o=0,i=[];++n<r;){var a=e[n],s=t?t(a):a;if(!n||!$s(s,u)){var u=s;i[o++]=0===a?0:a}}return i}function Xo(e){return"number"==typeof e?e:wu(e)?T:+e}function $o(e){if("string"==typeof e)return e;if(nu(e))return Xt(e,$o)+"";if(wu(e))return vr?vr.call(e):"";var t=e+"";return"0"==t&&1/e==-F?"-0":t}function Qo(e,t,n){var r=-1,i=qt,a=e.length,s=!0,u=[],c=u;if(n)s=!1,i=Yt;else if(a>=o){var f=t?null:zi(e);if(f)return xn(f);s=!1,i=Cn,c=new jr}else c=t?[]:u;e:for(;++r<a;){var l=e[r],p=t?t(l):l;if(l=n||0!==l?l:0,s&&p==p){for(var h=c.length;h--;)if(c[h]===p)continue e;t&&c.push(p),u.push(l)}else i(c,p,n)||(c!==u&&c.push(p),u.push(l))}return u}function ei(e,t){return null==(e=Ua(e,t=ui(t,e)))||delete e[Za(us(t))]}function ti(e,t,n,r){return Wo(e,t,n(oo(e,t)),r)}function ni(e,t,n,r){for(var o=e.length,i=r?o:-1;(r?i--:++i<o)&&t(e[i],i,e););return n?Ko(e,r?0:i,r?i+1:o):Ko(e,r?i+1:0,r?o:i)}function ri(e,t){var n=e;return n instanceof Rr&&(n=n.value()),Qt(t,function(e,t){return t.func.apply(t.thisArg,$t([e],t.args))},n)}function oi(e,t,r){var o=e.length;if(o<2)return o?Qo(e[0]):[];for(var i=-1,a=n(o);++i<o;)for(var s=e[i],u=-1;++u<o;)u!=i&&(a[i]=Br(a[i]||s,e[u],t,r));return Qo($r(a,1),t,r)}function ii(e,t,n){for(var o=-1,i=e.length,a=t.length,s={};++o<i;){var u=o<a?t[o]:r;n(s,e[o],u)}return s}function ai(e){return iu(e)?e:[]}function si(e){return"function"==typeof e?e:_c}function ui(e,t){return nu(e)?e:ja(e,t)?[e]:za(Mu(e))}var ci=ko;function fi(e,t,n){var o=e.length;return n=n===r?o:n,!t&&n>=o?e:Ko(e,t,n)}var li=nn||function(e){return Ft.clearTimeout(e)};function pi(e,t){if(t)return e.slice();var n=e.length,r=wt?wt(n):new e.constructor(n);return e.copy(r),r}function hi(e){var t=new e.constructor(e.byteLength);return new mt(t).set(new mt(e)),t}function di(e,t){var n=t?hi(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}function _i(e){var t=new e.constructor(e.source,Ve.exec(e));return t.lastIndex=e.lastIndex,t}function gi(e){return gr?Qe(gr.call(e)):{}}function vi(e,t){var n=t?hi(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function yi(e,t){if(e!==t){var n=e!==r,o=null===e,i=e==e,a=wu(e),s=t!==r,u=null===t,c=t==t,f=wu(t);if(!u&&!f&&!a&&e>t||a&&s&&c&&!u&&!f||o&&s&&c||!n&&c||!i)return 1;if(!o&&!a&&!f&&e<t||f&&n&&i&&!o&&!a||u&&n&&i||!s&&i||!c)return-1}return 0}function mi(e,t,n){for(var r=-1,o=e.criteria,i=t.criteria,a=o.length,s=n.length;++r<a;){var u=yi(o[r],i[r]);if(u)return r>=s?u:u*("desc"==n[r]?-1:1)}return e.index-t.index}function wi(e,t,r,o){for(var i=-1,a=e.length,s=r.length,u=-1,c=t.length,f=Yn(a-s,0),l=n(c+f),p=!o;++u<c;)l[u]=t[u];for(;++i<s;)(p||i<a)&&(l[r[i]]=e[i]);for(;f--;)l[u++]=e[i++];return l}function Ci(e,t,r,o){for(var i=-1,a=e.length,s=-1,u=r.length,c=-1,f=t.length,l=Yn(a-u,0),p=n(l+f),h=!o;++i<l;)p[i]=e[i];for(var d=i;++c<f;)p[d+c]=t[c];for(;++s<u;)(h||i<a)&&(p[d+r[s]]=e[i++]);return p}function Ri(e,t){var r=-1,o=e.length;for(t||(t=n(o));++r<o;)t[r]=e[r];return t}function Ai(e,t,n,o){var i=!n;n||(n={});for(var a=-1,s=t.length;++a<s;){var u=t[a],c=o?o(n[u],e[u],u,n,e):r;c===r&&(c=e[u]),i?Ur(n,u,c):Jr(n,u,c)}return n}function bi(e,t){return Ai(e,ha(e),t)}function Oi(e,t){return Ai(e,da(e),t)}function ji(e,t){return function(n,r){var o=nu(n)?Dt:xr,i=t?t():{};return o(n,e,ua(r,2),i)}}function Pi(e){return ko(function(t,n){var o=-1,i=n.length,a=i>1?n[i-1]:r,s=i>2?n[2]:r;for(a=e.length>3&&"function"==typeof a?(i--,a):r,s&&Oa(n[0],n[1],s)&&(a=i<3?r:a,i=1),t=Qe(t);++o<i;){var u=n[o];u&&e(t,u,o,a)}return t})}function Ei(e,t){return function(n,r){if(null==n)return n;if(!ou(n))return e(n,r);for(var o=n.length,i=t?o:-1,a=Qe(n);(t?i--:++i<o)&&!1!==r(a[i],i,a););return n}}function Ii(e){return function(t,n,r){for(var o=-1,i=Qe(t),a=r(t),s=a.length;s--;){var u=a[e?s:++o];if(!1===n(i[u],u,i))break}return t}}function Mi(e,t,n){var r=t&_,o=Ji(e);return function t(){return(this&&this!==Ft&&this instanceof t?o:e).apply(r?n:this,arguments)}}function Fi(e){return function(t){var n=In(t=Mu(t))?Gn(t):r,o=n?n[0]:t.charAt(0),i=n?fi(n,1).join(""):t.slice(1);return o[e]()+i}}function Li(e){return function(t){return Qt(cc(tc(t).replace(gt,"")),e,"")}}function Ji(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=mr(e.prototype),r=e.apply(n,t);return pu(r)?r:n}}function Ti(e,t,o){var i=Ji(e);return function a(){for(var s=arguments.length,u=n(s),c=s,f=sa(a);c--;)u[c]=arguments[c];var l=s<3&&u[0]!==f&&u[s-1]!==f?[]:Tn(u,f);return(s-=l.length)<o?Bi(e,t,Hi,a.placeholder,r,u,l,r,r,o-s):Nt(this&&this!==Ft&&this instanceof a?i:e,this,u)}}function xi(e){return function(t,n,o){var i=Qe(t);if(!ou(t)){var a=ua(n,3);t=Nu(t),n=function(e){return a(i[e],e,i)}}var s=e(t,n,o);return s>-1?i[a?t[s]:s]:r}}function Si(e){return na(function(t){var n=t.length,o=n,i=Cr.prototype.thru;for(e&&t.reverse();o--;){var s=t[o];if("function"!=typeof s)throw new nt(a);if(i&&!u&&"wrapper"==aa(s))var u=new Cr([],!0)}for(o=u?o:n;++o<n;){var c=aa(s=t[o]),f="wrapper"==c?ia(s):r;u=f&&Ea(f[0])&&f[1]==(R|y|w|A)&&!f[4].length&&1==f[9]?u[aa(f[0])].apply(u,f[3]):1==s.length&&Ea(s)?u[c]():u.thru(s)}return function(){var e=arguments,r=e[0];if(u&&1==e.length&&nu(r))return u.plant(r).value();for(var o=0,i=n?t[o].apply(this,e):r;++o<n;)i=t[o].call(this,i);return i}})}function Hi(e,t,o,i,a,s,u,c,f,l){var p=t&R,h=t&_,d=t&g,v=t&(y|m),w=t&b,C=d?r:Ji(e);return function r(){for(var _=arguments.length,g=n(_),y=_;y--;)g[y]=arguments[y];if(v)var m=sa(r),R=bn(g,m);if(i&&(g=wi(g,i,a,v)),s&&(g=Ci(g,s,u,v)),_-=R,v&&_<l){var A=Tn(g,m);return Bi(e,t,Hi,r.placeholder,o,g,A,c,f,l-_)}var b=h?o:this,O=d?b[e]:e;return _=g.length,c?g=ka(g,c):w&&_>1&&g.reverse(),p&&f<_&&(g.length=f),this&&this!==Ft&&this instanceof r&&(O=C||Ji(O)),O.apply(b,g)}}function Ui(e,t){return function(n,r){return po(n,e,t(r),{})}}function ki(e,t){return function(n,o){var i;if(n===r&&o===r)return t;if(n!==r&&(i=n),o!==r){if(i===r)return o;"string"==typeof n||"string"==typeof o?(n=$o(n),o=$o(o)):(n=Xo(n),o=Xo(o)),i=e(n,o)}return i}}function Gi(e){return na(function(t){return t=Xt(t,mn(ua())),ko(function(n){var r=this;return e(t,function(e){return Nt(e,r,n)})})})}function Vi(e,t){var n=(t=t===r?" ":$o(t)).length;if(n<2)return n?Uo(t,e):t;var o=Uo(t,Hn(e/kn(t)));return In(t)?fi(Gn(o),0,e).join(""):o.slice(0,e)}function Wi(e,t,r,o){var i=t&_,a=Ji(e);return function t(){for(var s=-1,u=arguments.length,c=-1,f=o.length,l=n(f+u),p=this&&this!==Ft&&this instanceof t?a:e;++c<f;)l[c]=o[c];for(;u--;)l[c++]=arguments[++s];return Nt(p,i?r:this,l)}}function Ni(e){return function(t,n,o){return o&&"number"!=typeof o&&Oa(t,n,o)&&(n=o=r),t=Ou(t),n===r?(n=t,t=0):n=Ou(n),Ho(t,n,o=o===r?t<n?1:-1:Ou(o),e)}}function Di(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=Eu(t),n=Eu(n)),e(t,n)}}function Bi(e,t,n,o,i,a,s,u,c,f){var l=t&y;t|=l?w:C,(t&=~(l?C:w))&v||(t&=~(_|g));var p=[e,t,i,l?a:r,l?s:r,l?r:a,l?r:s,u,c,f],h=n.apply(r,p);return Ea(e)&&Va(h,p),h.placeholder=o,Da(h,e,t)}function Ki(e){var t=$e[e];return function(e,n){if(e=Eu(e),n=null==n?0:Xn(ju(n),292)){var r=(Mu(e)+"e").split("e");return+((r=(Mu(t(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))}return t(e)}}var zi=ir&&1/xn(new ir([,-0]))[1]==F?function(e){return new ir(e)}:wc;function Zi(e){return function(t){var n=_a(t);return n==Z?Ln(t):n==ee?Sn(t):yn(t,e(t))}}function qi(e,t,n,o,i,s,u,c){var f=t&g;if(!f&&"function"!=typeof e)throw new nt(a);var l=o?o.length:0;if(l||(t&=~(w|C),o=i=r),u=u===r?u:Yn(ju(u),0),c=c===r?c:ju(c),l-=i?i.length:0,t&C){var p=o,h=i;o=i=r}var d=f?r:ia(e),v=[e,t,n,o,i,p,h,s,u,c];if(d&&Ta(v,d),e=v[0],t=v[1],n=v[2],o=v[3],i=v[4],!(c=v[9]=v[9]===r?f?0:e.length:Yn(v[9]-l,0))&&t&(y|m)&&(t&=~(y|m)),t&&t!=_)R=t==y||t==m?Ti(e,t,c):t!=w&&t!=(_|w)||i.length?Hi.apply(r,v):Wi(e,t,n,o);else var R=Mi(e,t,n);return Da((d?No:Va)(R,v),e,t)}function Yi(e,t,n,o){return e===r||$s(e,it[n])&&!ut.call(o,n)?t:e}function Xi(e,t,n,o,i,a){return pu(e)&&pu(t)&&(a.set(t,e),Po(e,t,r,Xi,a),a.delete(t)),e}function $i(e){return gu(e)?r:e}function Qi(e,t,n,o,i,a){var s=n&h,u=e.length,c=t.length;if(u!=c&&!(s&&c>u))return!1;var f=a.get(e);if(f&&a.get(t))return f==t;var l=-1,p=!0,_=n&d?new jr:r;for(a.set(e,t),a.set(t,e);++l<u;){var g=e[l],v=t[l];if(o)var y=s?o(v,g,l,t,e,a):o(g,v,l,e,t,a);if(y!==r){if(y)continue;p=!1;break}if(_){if(!tn(t,function(e,t){if(!Cn(_,t)&&(g===e||i(g,e,n,o,a)))return _.push(t)})){p=!1;break}}else if(g!==v&&!i(g,v,n,o,a)){p=!1;break}}return a.delete(e),a.delete(t),p}function ea(e,t,n,r,o,i,a){switch(n){case se:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case ae:return!(e.byteLength!=t.byteLength||!i(new mt(e),new mt(t)));case W:case N:case q:return $s(+e,+t);case B:return e.name==t.name&&e.message==t.message;case Q:case te:return e==t+"";case Z:var s=Ln;case ee:var u=r&h;if(s||(s=xn),e.size!=t.size&&!u)return!1;var c=a.get(e);if(c)return c==t;r|=d,a.set(e,t);var f=Qi(s(e),s(t),r,o,i,a);return a.delete(e),f;case ne:if(gr)return gr.call(e)==gr.call(t)}return!1}function ta(e,t,n,o,i,a){var s=n&h,u=ra(e),c=u.length;if(c!=ra(t).length&&!s)return!1;for(var f=c;f--;){var l=u[f];if(!(s?l in t:ut.call(t,l)))return!1}var p=a.get(e);if(p&&a.get(t))return p==t;var d=!0;a.set(e,t),a.set(t,e);for(var _=s;++f<c;){var g=e[l=u[f]],v=t[l];if(o)var y=s?o(v,g,l,t,e,a):o(g,v,l,e,t,a);if(!(y===r?g===v||i(g,v,n,o,a):y)){d=!1;break}_||(_="constructor"==l)}if(d&&!_){var m=e.constructor,w=t.constructor;m!=w&&"constructor"in e&&"constructor"in t&&!("function"==typeof m&&m instanceof m&&"function"==typeof w&&w instanceof w)&&(d=!1)}return a.delete(e),a.delete(t),d}function na(e){return Na(Ha(e,r,rs),e+"")}function ra(e){return io(e,Nu,ha)}function oa(e){return io(e,Du,da)}var ia=ur?function(e){return ur.get(e)}:wc;function aa(e){for(var t=e.name+"",n=cr[t],r=ut.call(cr,t)?n.length:0;r--;){var o=n[r],i=o.func;if(null==i||i==e)return o.name}return t}function sa(e){return(ut.call(yr,"placeholder")?yr:e).placeholder}function ua(){var e=yr.iteratee||gc;return e=e===gc?wo:e,arguments.length?e(arguments[0],arguments[1]):e}function ca(e,t){var n=e.__data__;return Pa(t)?n["string"==typeof t?"string":"hash"]:n.map}function fa(e){for(var t=Nu(e),n=t.length;n--;){var r=t[n],o=e[r];t[n]=[r,o,La(o)]}return t}function la(e,t){var n=En(e,t);return mo(n)?n:r}function pa(e){var t=ut.call(e,xt),n=e[xt];try{e[xt]=r;var o=!0}catch(e){}var i=lt.call(e);return o&&(t?e[xt]=n:delete e[xt]),i}var ha=Nn?function(e){return null==e?[]:(e=Qe(e),Zt(Nn(e),function(t){return It.call(e,t)}))}:Pc,da=Nn?function(e){for(var t=[];e;)$t(t,ha(e)),e=Ct(e);return t}:Pc,_a=ao;function ga(e,t,n){for(var r=-1,o=n.length;++r<o;){var i=n[r],a=i.size;switch(i.type){case"drop":e+=a;break;case"dropRight":t-=a;break;case"take":t=Xn(t,e+a);break;case"takeRight":e=Yn(e,t-a)}}return{start:e,end:t}}function va(e){var t=e.match(Se);return t?t[1].split(He):[]}function ya(e,t,n){for(var r=-1,o=(t=ui(t,e)).length,i=!1;++r<o;){var a=Za(t[r]);if(!(i=null!=e&&n(e,a)))break;e=e[a]}return i||++r!=o?i:!!(o=null==e?0:e.length)&&lu(o)&&ba(a,o)&&(nu(e)||tu(e))}function ma(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&ut.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function wa(e){return"function"!=typeof e.constructor||Fa(e)?{}:mr(Ct(e))}function Ca(e,t,n){var r=e.constructor;switch(t){case ae:return hi(e);case W:case N:return new r(+e);case se:return di(e,n);case ue:case ce:case fe:case le:case pe:case he:case de:case _e:case ge:return vi(e,n);case Z:return new r;case q:case te:return new r(e);case Q:return _i(e);case ee:return new r;case ne:return gi(e)}}function Ra(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(xe,"{\n/* [wrapped with "+t+"] */\n")}function Aa(e){return nu(e)||tu(e)||!!(Lt&&e&&e[Lt])}function ba(e,t){var n=typeof e;return!!(t=null==t?L:t)&&("number"==n||"symbol"!=n&&Ke.test(e))&&e>-1&&e%1==0&&e<t}function Oa(e,t,n){if(!pu(n))return!1;var r=typeof t;return!!("number"==r?ou(n)&&ba(t,n.length):"string"==r&&t in n)&&$s(n[t],e)}function ja(e,t){if(nu(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!wu(e))||Ee.test(e)||!Pe.test(e)||null!=t&&e in Qe(t)}function Pa(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function Ea(e){var t=aa(e),n=yr[t];if("function"!=typeof n||!(t in Rr.prototype))return!1;if(e===n)return!0;var r=ia(n);return!!r&&e===r[0]}function Ia(e){return!!ft&&ft in e}(nr&&_a(new nr(new ArrayBuffer(1)))!=se||rr&&_a(new rr)!=Z||or&&"[object Promise]"!=_a(or.resolve())||ir&&_a(new ir)!=ee||ar&&_a(new ar)!=oe)&&(_a=function(e){var t=ao(e),n=t==X?e.constructor:r,o=n?qa(n):"";if(o)switch(o){case fr:return se;case lr:return Z;case pr:return"[object Promise]";case hr:return ee;case dr:return oe}return t});var Ma=at?cu:Ec;function Fa(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||it)}function La(e){return e==e&&!pu(e)}function Ja(e,t){return function(n){return null!=n&&n[e]===t&&(t!==r||e in Qe(n))}}function Ta(e,t){var n=e[1],r=t[1],o=n|r,i=o<(_|g|R),a=r==R&&n==y||r==R&&n==A&&e[7].length<=t[8]||r==(R|A)&&t[7].length<=t[8]&&n==y;if(!i&&!a)return e;r&_&&(e[2]=t[2],o|=n&_?0:v);var s=t[3];if(s){var u=e[3];e[3]=u?wi(u,s,t[4]):s,e[4]=u?Tn(e[3],c):t[4]}return(s=t[5])&&(u=e[5],e[5]=u?Ci(u,s,t[6]):s,e[6]=u?Tn(e[5],c):t[6]),(s=t[7])&&(e[7]=s),r&R&&(e[8]=null==e[8]?t[8]:Xn(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=o,e}function xa(e){var t=[];if(null!=e)for(var n in Qe(e))t.push(n);return t}function Sa(e){return lt.call(e)}function Ha(e,t,o){return t=Yn(t===r?e.length-1:t,0),function(){for(var r=arguments,i=-1,a=Yn(r.length-t,0),s=n(a);++i<a;)s[i]=r[t+i];i=-1;for(var u=n(t+1);++i<t;)u[i]=r[i];return u[t]=o(s),Nt(e,this,u)}}function Ua(e,t){return t.length<2?e:oo(e,Ko(t,0,-1))}function ka(e,t){for(var n=e.length,o=Xn(t.length,n),i=Ri(e);o--;){var a=t[o];e[o]=ba(a,n)?i[a]:r}return e}function Ga(e,t){if("__proto__"!=t)return e[t]}var Va=Ba(No),Wa=hn||function(e,t){return Ft.setTimeout(e,t)},Na=Ba(Do);function Da(e,t,n){var r=t+"";return Na(e,Ra(r,Ya(va(r),n)))}function Ba(e){var t=0,n=0;return function(){var o=$n(),i=E-(o-n);if(n=o,i>0){if(++t>=P)return arguments[0]}else t=0;return e.apply(r,arguments)}}function Ka(e,t){var n=-1,o=e.length,i=o-1;for(t=t===r?o:t;++n<t;){var a=So(n,i),s=e[a];e[a]=e[n],e[n]=s}return e.length=t,e}var za=function(e){var t=Ks(e,function(e){return n.size===u&&n.clear(),e}),n=t.cache;return t}(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ie,function(e,n,r,o){t.push(r?o.replace(ke,"$1"):n||e)}),t});function Za(e){if("string"==typeof e||wu(e))return e;var t=e+"";return"0"==t&&1/e==-F?"-0":t}function qa(e){if(null!=e){try{return st.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Ya(e,t){return Bt(U,function(n){var r="_."+n[0];t&n[1]&&!qt(e,r)&&e.push(r)}),e.sort()}function Xa(e){if(e instanceof Rr)return e.clone();var t=new Cr(e.__wrapped__,e.__chain__);return t.__actions__=Ri(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var $a=ko(function(e,t){return iu(e)?Br(e,$r(t,1,iu,!0)):[]}),Qa=ko(function(e,t){var n=us(t);return iu(n)&&(n=r),iu(e)?Br(e,$r(t,1,iu,!0),ua(n,2)):[]}),es=ko(function(e,t){var n=us(t);return iu(n)&&(n=r),iu(e)?Br(e,$r(t,1,iu,!0),r,n):[]});function ts(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:ju(n);return o<0&&(o=Yn(r+o,0)),sn(e,ua(t,3),o)}function ns(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var i=o-1;return n!==r&&(i=ju(n),i=n<0?Yn(o+i,0):Xn(i,o-1)),sn(e,ua(t,3),i,!0)}function rs(e){return null!=e&&e.length?$r(e,1):[]}function os(e){return e&&e.length?e[0]:r}var is=ko(function(e){var t=Xt(e,ai);return t.length&&t[0]===e[0]?lo(t):[]}),as=ko(function(e){var t=us(e),n=Xt(e,ai);return t===us(n)?t=r:n.pop(),n.length&&n[0]===e[0]?lo(n,ua(t,2)):[]}),ss=ko(function(e){var t=us(e),n=Xt(e,ai);return(t="function"==typeof t?t:r)&&n.pop(),n.length&&n[0]===e[0]?lo(n,r,t):[]});function us(e){var t=null==e?0:e.length;return t?e[t-1]:r}var cs=ko(fs);function fs(e,t){return e&&e.length&&t&&t.length?To(e,t):e}var ls=na(function(e,t){var n=null==e?0:e.length,r=kr(e,t);return xo(e,Xt(t,function(e){return ba(e,n)?+e:e}).sort(yi)),r});function ps(e){return null==e?e:tr.call(e)}var hs=ko(function(e){return Qo($r(e,1,iu,!0))}),ds=ko(function(e){var t=us(e);return iu(t)&&(t=r),Qo($r(e,1,iu,!0),ua(t,2))}),_s=ko(function(e){var t=us(e);return t="function"==typeof t?t:r,Qo($r(e,1,iu,!0),r,t)});function gs(e){if(!e||!e.length)return[];var t=0;return e=Zt(e,function(e){if(iu(e))return t=Yn(e.length,t),!0}),vn(t,function(t){return Xt(e,pn(t))})}function vs(e,t){if(!e||!e.length)return[];var n=gs(e);return null==t?n:Xt(n,function(e){return Nt(t,r,e)})}var ys=ko(function(e,t){return iu(e)?Br(e,t):[]}),ms=ko(function(e){return oi(Zt(e,iu))}),ws=ko(function(e){var t=us(e);return iu(t)&&(t=r),oi(Zt(e,iu),ua(t,2))}),Cs=ko(function(e){var t=us(e);return t="function"==typeof t?t:r,oi(Zt(e,iu),r,t)}),Rs=ko(gs);var As=ko(function(e){var t=e.length,n=t>1?e[t-1]:r;return vs(e,n="function"==typeof n?(e.pop(),n):r)});function bs(e){var t=yr(e);return t.__chain__=!0,t}function Os(e,t){return t(e)}var js=na(function(e){var t=e.length,n=t?e[0]:0,o=this.__wrapped__,i=function(t){return kr(t,e)};return!(t>1||this.__actions__.length)&&o instanceof Rr&&ba(n)?((o=o.slice(n,+n+(t?1:0))).__actions__.push({func:Os,args:[i],thisArg:r}),new Cr(o,this.__chain__).thru(function(e){return t&&!e.length&&e.push(r),e})):this.thru(i)});var Ps=ji(function(e,t,n){ut.call(e,n)?++e[n]:Ur(e,n,1)});var Es=xi(ts),Is=xi(ns);function Ms(e,t){return(nu(e)?Bt:Kr)(e,ua(t,3))}function Fs(e,t){return(nu(e)?Kt:zr)(e,ua(t,3))}var Ls=ji(function(e,t,n){ut.call(e,n)?e[n].push(t):Ur(e,n,[t])});var Js=ko(function(e,t,r){var o=-1,i="function"==typeof t,a=ou(e)?n(e.length):[];return Kr(e,function(e){a[++o]=i?Nt(t,e,r):ho(e,t,r)}),a}),Ts=ji(function(e,t,n){Ur(e,n,t)});function xs(e,t){return(nu(e)?Xt:bo)(e,ua(t,3))}var Ss=ji(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]});var Hs=ko(function(e,t){if(null==e)return[];var n=t.length;return n>1&&Oa(e,t[0],t[1])?t=[]:n>2&&Oa(t[0],t[1],t[2])&&(t=[t[0]]),Mo(e,$r(t,1),[])}),Us=rn||function(){return Ft.Date.now()};function ks(e,t,n){return t=n?r:t,t=e&&null==t?e.length:t,qi(e,R,r,r,r,r,t)}function Gs(e,t){var n;if("function"!=typeof t)throw new nt(a);return e=ju(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=r),n}}var Vs=ko(function(e,t,n){var r=_;if(n.length){var o=Tn(n,sa(Vs));r|=w}return qi(e,r,t,n,o)}),Ws=ko(function(e,t,n){var r=_|g;if(n.length){var o=Tn(n,sa(Ws));r|=w}return qi(t,r,e,n,o)});function Ns(e,t,n){var o,i,s,u,c,f,l=0,p=!1,h=!1,d=!0;if("function"!=typeof e)throw new nt(a);function _(t){var n=o,a=i;return o=i=r,l=t,u=e.apply(a,n)}function g(e){return l=e,c=Wa(m,t),p?_(e):u}function v(e){var n=t-(e-f);return h?Xn(n,s-(e-l)):n}function y(e){var n=e-f;return f===r||n>=t||n<0||h&&e-l>=s}function m(){var e=Us();if(y(e))return w(e);c=Wa(m,v(e))}function w(e){return c=r,d&&o?_(e):(o=i=r,u)}function C(){var e=Us(),n=y(e);if(o=arguments,i=this,f=e,n){if(c===r)return g(f);if(h)return c=Wa(m,t),_(f)}return c===r&&(c=Wa(m,t)),u}return t=Eu(t)||0,pu(n)&&(p=!!n.leading,s=(h="maxWait"in n)?Yn(Eu(n.maxWait)||0,t):s,d="trailing"in n?!!n.trailing:d),C.cancel=function(){c!==r&&li(c),l=0,o=f=i=c=r},C.flush=function(){return c===r?u:w(Us())},C}var Ds=ko(function(e,t){return Dr(e,1,t)}),Bs=ko(function(e,t,n){return Dr(e,Eu(t)||0,n)});function Ks(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new nt(a);var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var a=e.apply(this,r);return n.cache=i.set(o,a)||i,a};return n.cache=new(Ks.Cache||Or),n}function zs(e){if("function"!=typeof e)throw new nt(a);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}Ks.Cache=Or;var Zs=ci(function(e,t){var n=(t=1==t.length&&nu(t[0])?Xt(t[0],mn(ua())):Xt($r(t,1),mn(ua()))).length;return ko(function(r){for(var o=-1,i=Xn(r.length,n);++o<i;)r[o]=t[o].call(this,r[o]);return Nt(e,this,r)})}),qs=ko(function(e,t){var n=Tn(t,sa(qs));return qi(e,w,r,t,n)}),Ys=ko(function(e,t){var n=Tn(t,sa(Ys));return qi(e,C,r,t,n)}),Xs=na(function(e,t){return qi(e,A,r,r,r,t)});function $s(e,t){return e===t||e!=e&&t!=t}var Qs=Di(so),eu=Di(function(e,t){return e>=t}),tu=_o(function(){return arguments}())?_o:function(e){return hu(e)&&ut.call(e,"callee")&&!It.call(e,"callee")},nu=n.isArray,ru=Ht?mn(Ht):function(e){return hu(e)&&ao(e)==ae};function ou(e){return null!=e&&lu(e.length)&&!cu(e)}function iu(e){return hu(e)&&ou(e)}var au=Kn||Ec,su=Ut?mn(Ut):function(e){return hu(e)&&ao(e)==N};function uu(e){if(!hu(e))return!1;var t=ao(e);return t==B||t==D||"string"==typeof e.message&&"string"==typeof e.name&&!gu(e)}function cu(e){if(!pu(e))return!1;var t=ao(e);return t==K||t==z||t==V||t==$}function fu(e){return"number"==typeof e&&e==ju(e)}function lu(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=L}function pu(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function hu(e){return null!=e&&"object"==typeof e}var du=kt?mn(kt):function(e){return hu(e)&&_a(e)==Z};function _u(e){return"number"==typeof e||hu(e)&&ao(e)==q}function gu(e){if(!hu(e)||ao(e)!=X)return!1;var t=Ct(e);if(null===t)return!0;var n=ut.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&st.call(n)==pt}var vu=Gt?mn(Gt):function(e){return hu(e)&&ao(e)==Q};var yu=Vt?mn(Vt):function(e){return hu(e)&&_a(e)==ee};function mu(e){return"string"==typeof e||!nu(e)&&hu(e)&&ao(e)==te}function wu(e){return"symbol"==typeof e||hu(e)&&ao(e)==ne}var Cu=Wt?mn(Wt):function(e){return hu(e)&&lu(e.length)&&!!bt[ao(e)]};var Ru=Di(Ao),Au=Di(function(e,t){return e<=t});function bu(e){if(!e)return[];if(ou(e))return mu(e)?Gn(e):Ri(e);if(Jt&&e[Jt])return Fn(e[Jt]());var t=_a(e);return(t==Z?Ln:t==ee?xn:$u)(e)}function Ou(e){return e?(e=Eu(e))===F||e===-F?(e<0?-1:1)*J:e==e?e:0:0===e?e:0}function ju(e){var t=Ou(e),n=t%1;return t==t?n?t-n:t:0}function Pu(e){return e?Gr(ju(e),0,x):0}function Eu(e){if("number"==typeof e)return e;if(wu(e))return T;if(pu(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=pu(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(Le,"");var n=Ne.test(e);return n||Be.test(e)?Et(e.slice(2),n?2:8):We.test(e)?T:+e}function Iu(e){return Ai(e,Du(e))}function Mu(e){return null==e?"":$o(e)}var Fu=Pi(function(e,t){if(Fa(t)||ou(t))Ai(t,Nu(t),e);else for(var n in t)ut.call(t,n)&&Jr(e,n,t[n])}),Lu=Pi(function(e,t){Ai(t,Du(t),e)}),Ju=Pi(function(e,t,n,r){Ai(t,Du(t),e,r)}),Tu=Pi(function(e,t,n,r){Ai(t,Nu(t),e,r)}),xu=na(kr);var Su=ko(function(e,t){e=Qe(e);var n=-1,o=t.length,i=o>2?t[2]:r;for(i&&Oa(t[0],t[1],i)&&(o=1);++n<o;)for(var a=t[n],s=Du(a),u=-1,c=s.length;++u<c;){var f=s[u],l=e[f];(l===r||$s(l,it[f])&&!ut.call(e,f))&&(e[f]=a[f])}return e}),Hu=ko(function(e){return e.push(r,Xi),Nt(Ku,r,e)});function Uu(e,t,n){var o=null==e?r:oo(e,t);return o===r?n:o}function ku(e,t){return null!=e&&ya(e,t,co)}var Gu=Ui(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=lt.call(t)),e[t]=n},pc(_c)),Vu=Ui(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=lt.call(t)),ut.call(e,t)?e[t].push(n):e[t]=[n]},ua),Wu=ko(ho);function Nu(e){return ou(e)?Er(e):Co(e)}function Du(e){return ou(e)?Er(e,!0):Ro(e)}var Bu=Pi(function(e,t,n){Po(e,t,n)}),Ku=Pi(function(e,t,n,r){Po(e,t,n,r)}),zu=na(function(e,t){var n={};if(null==e)return n;var r=!1;t=Xt(t,function(t){return t=ui(t,e),r||(r=t.length>1),t}),Ai(e,oa(e),n),r&&(n=Vr(n,f|l|p,$i));for(var o=t.length;o--;)ei(n,t[o]);return n});var Zu=na(function(e,t){return null==e?{}:Fo(e,t)});function qu(e,t){if(null==e)return{};var n=Xt(oa(e),function(e){return[e]});return t=ua(t),Lo(e,n,function(e,n){return t(e,n[0])})}var Yu=Zi(Nu),Xu=Zi(Du);function $u(e){return null==e?[]:wn(e,Nu(e))}var Qu=Li(function(e,t,n){return t=t.toLowerCase(),e+(n?ec(t):t)});function ec(e){return uc(Mu(e).toLowerCase())}function tc(e){return(e=Mu(e))&&e.replace(ze,On).replace(vt,"")}var nc=Li(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),rc=Li(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),oc=Fi("toLowerCase");var ic=Li(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()});var ac=Li(function(e,t,n){return e+(n?" ":"")+uc(t)});var sc=Li(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),uc=Fi("toUpperCase");function cc(e,t,n){return e=Mu(e),(t=n?r:t)===r?Mn(e)?Dn(e):on(e):e.match(t)||[]}var fc=ko(function(e,t){try{return Nt(e,r,t)}catch(e){return uu(e)?e:new Ye(e)}}),lc=na(function(e,t){return Bt(t,function(t){t=Za(t),Ur(e,t,Vs(e[t],e))}),e});function pc(e){return function(){return e}}var hc=Si(),dc=Si(!0);function _c(e){return e}function gc(e){return wo("function"==typeof e?e:Vr(e,f))}var vc=ko(function(e,t){return function(n){return ho(n,e,t)}}),yc=ko(function(e,t){return function(n){return ho(e,n,t)}});function mc(e,t,n){var r=Nu(t),o=ro(t,r);null!=n||pu(t)&&(o.length||!r.length)||(n=t,t=e,e=this,o=ro(t,Nu(t)));var i=!(pu(n)&&"chain"in n&&!n.chain),a=cu(e);return Bt(o,function(n){var r=t[n];e[n]=r,a&&(e.prototype[n]=function(){var t=this.__chain__;if(i||t){var n=e(this.__wrapped__);return(n.__actions__=Ri(this.__actions__)).push({func:r,args:arguments,thisArg:e}),n.__chain__=t,n}return r.apply(e,$t([this.value()],arguments))})}),e}function wc(){}var Cc=Gi(Xt),Rc=Gi(zt),Ac=Gi(tn);function bc(e){return ja(e)?pn(Za(e)):Jo(e)}var Oc=Ni(),jc=Ni(!0);function Pc(){return[]}function Ec(){return!1}var Ic=ki(function(e,t){return e+t},0),Mc=Ki("ceil"),Fc=ki(function(e,t){return e/t},1),Lc=Ki("floor");var Jc=ki(function(e,t){return e*t},1),Tc=Ki("round"),xc=ki(function(e,t){return e-t},0);return yr.after=function(e,t){if("function"!=typeof t)throw new nt(a);return e=ju(e),function(){if(--e<1)return t.apply(this,arguments)}},yr.ary=ks,yr.assign=Fu,yr.assignIn=Lu,yr.assignInWith=Ju,yr.assignWith=Tu,yr.at=xu,yr.before=Gs,yr.bind=Vs,yr.bindAll=lc,yr.bindKey=Ws,yr.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return nu(e)?e:[e]},yr.chain=bs,yr.chunk=function(e,t,o){t=(o?Oa(e,t,o):t===r)?1:Yn(ju(t),0);var i=null==e?0:e.length;if(!i||t<1)return[];for(var a=0,s=0,u=n(Hn(i/t));a<i;)u[s++]=Ko(e,a,a+=t);return u},yr.compact=function(e){for(var t=-1,n=null==e?0:e.length,r=0,o=[];++t<n;){var i=e[t];i&&(o[r++]=i)}return o},yr.concat=function(){var e=arguments.length;if(!e)return[];for(var t=n(e-1),r=arguments[0],o=e;o--;)t[o-1]=arguments[o];return $t(nu(r)?Ri(r):[r],$r(t,1))},yr.cond=function(e){var t=null==e?0:e.length,n=ua();return e=t?Xt(e,function(e){if("function"!=typeof e[1])throw new nt(a);return[n(e[0]),e[1]]}):[],ko(function(n){for(var r=-1;++r<t;){var o=e[r];if(Nt(o[0],this,n))return Nt(o[1],this,n)}})},yr.conforms=function(e){return Wr(Vr(e,f))},yr.constant=pc,yr.countBy=Ps,yr.create=function(e,t){var n=mr(e);return null==t?n:Sr(n,t)},yr.curry=function e(t,n,o){var i=qi(t,y,r,r,r,r,r,n=o?r:n);return i.placeholder=e.placeholder,i},yr.curryRight=function e(t,n,o){var i=qi(t,m,r,r,r,r,r,n=o?r:n);return i.placeholder=e.placeholder,i},yr.debounce=Ns,yr.defaults=Su,yr.defaultsDeep=Hu,yr.defer=Ds,yr.delay=Bs,yr.difference=$a,yr.differenceBy=Qa,yr.differenceWith=es,yr.drop=function(e,t,n){var o=null==e?0:e.length;return o?Ko(e,(t=n||t===r?1:ju(t))<0?0:t,o):[]},yr.dropRight=function(e,t,n){var o=null==e?0:e.length;return o?Ko(e,0,(t=o-(t=n||t===r?1:ju(t)))<0?0:t):[]},yr.dropRightWhile=function(e,t){return e&&e.length?ni(e,ua(t,3),!0,!0):[]},yr.dropWhile=function(e,t){return e&&e.length?ni(e,ua(t,3),!0):[]},yr.fill=function(e,t,n,r){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Oa(e,t,n)&&(n=0,r=o),Yr(e,t,n,r)):[]},yr.filter=function(e,t){return(nu(e)?Zt:Xr)(e,ua(t,3))},yr.flatMap=function(e,t){return $r(xs(e,t),1)},yr.flatMapDeep=function(e,t){return $r(xs(e,t),F)},yr.flatMapDepth=function(e,t,n){return n=n===r?1:ju(n),$r(xs(e,t),n)},yr.flatten=rs,yr.flattenDeep=function(e){return null!=e&&e.length?$r(e,F):[]},yr.flattenDepth=function(e,t){return null!=e&&e.length?$r(e,t=t===r?1:ju(t)):[]},yr.flip=function(e){return qi(e,b)},yr.flow=hc,yr.flowRight=dc,yr.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,r={};++t<n;){var o=e[t];r[o[0]]=o[1]}return r},yr.functions=function(e){return null==e?[]:ro(e,Nu(e))},yr.functionsIn=function(e){return null==e?[]:ro(e,Du(e))},yr.groupBy=Ls,yr.initial=function(e){return null!=e&&e.length?Ko(e,0,-1):[]},yr.intersection=is,yr.intersectionBy=as,yr.intersectionWith=ss,yr.invert=Gu,yr.invertBy=Vu,yr.invokeMap=Js,yr.iteratee=gc,yr.keyBy=Ts,yr.keys=Nu,yr.keysIn=Du,yr.map=xs,yr.mapKeys=function(e,t){var n={};return t=ua(t,3),to(e,function(e,r,o){Ur(n,t(e,r,o),e)}),n},yr.mapValues=function(e,t){var n={};return t=ua(t,3),to(e,function(e,r,o){Ur(n,r,t(e,r,o))}),n},yr.matches=function(e){return Oo(Vr(e,f))},yr.matchesProperty=function(e,t){return jo(e,Vr(t,f))},yr.memoize=Ks,yr.merge=Bu,yr.mergeWith=Ku,yr.method=vc,yr.methodOf=yc,yr.mixin=mc,yr.negate=zs,yr.nthArg=function(e){return e=ju(e),ko(function(t){return Io(t,e)})},yr.omit=zu,yr.omitBy=function(e,t){return qu(e,zs(ua(t)))},yr.once=function(e){return Gs(2,e)},yr.orderBy=function(e,t,n,o){return null==e?[]:(nu(t)||(t=null==t?[]:[t]),nu(n=o?r:n)||(n=null==n?[]:[n]),Mo(e,t,n))},yr.over=Cc,yr.overArgs=Zs,yr.overEvery=Rc,yr.overSome=Ac,yr.partial=qs,yr.partialRight=Ys,yr.partition=Ss,yr.pick=Zu,yr.pickBy=qu,yr.property=bc,yr.propertyOf=function(e){return function(t){return null==e?r:oo(e,t)}},yr.pull=cs,yr.pullAll=fs,yr.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?To(e,t,ua(n,2)):e},yr.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?To(e,t,r,n):e},yr.pullAt=ls,yr.range=Oc,yr.rangeRight=jc,yr.rearg=Xs,yr.reject=function(e,t){return(nu(e)?Zt:Xr)(e,zs(ua(t,3)))},yr.remove=function(e,t){var n=[];if(!e||!e.length)return n;var r=-1,o=[],i=e.length;for(t=ua(t,3);++r<i;){var a=e[r];t(a,r,e)&&(n.push(a),o.push(r))}return xo(e,o),n},yr.rest=function(e,t){if("function"!=typeof e)throw new nt(a);return ko(e,t=t===r?t:ju(t))},yr.reverse=ps,yr.sampleSize=function(e,t,n){return t=(n?Oa(e,t,n):t===r)?1:ju(t),(nu(e)?Mr:Vo)(e,t)},yr.set=function(e,t,n){return null==e?e:Wo(e,t,n)},yr.setWith=function(e,t,n,o){return o="function"==typeof o?o:r,null==e?e:Wo(e,t,n,o)},yr.shuffle=function(e){return(nu(e)?Fr:Bo)(e)},yr.slice=function(e,t,n){var o=null==e?0:e.length;return o?(n&&"number"!=typeof n&&Oa(e,t,n)?(t=0,n=o):(t=null==t?0:ju(t),n=n===r?o:ju(n)),Ko(e,t,n)):[]},yr.sortBy=Hs,yr.sortedUniq=function(e){return e&&e.length?Yo(e):[]},yr.sortedUniqBy=function(e,t){return e&&e.length?Yo(e,ua(t,2)):[]},yr.split=function(e,t,n){return n&&"number"!=typeof n&&Oa(e,t,n)&&(t=n=r),(n=n===r?x:n>>>0)?(e=Mu(e))&&("string"==typeof t||null!=t&&!vu(t))&&!(t=$o(t))&&In(e)?fi(Gn(e),0,n):e.split(t,n):[]},yr.spread=function(e,t){if("function"!=typeof e)throw new nt(a);return t=null==t?0:Yn(ju(t),0),ko(function(n){var r=n[t],o=fi(n,0,t);return r&&$t(o,r),Nt(e,this,o)})},yr.tail=function(e){var t=null==e?0:e.length;return t?Ko(e,1,t):[]},yr.take=function(e,t,n){return e&&e.length?Ko(e,0,(t=n||t===r?1:ju(t))<0?0:t):[]},yr.takeRight=function(e,t,n){var o=null==e?0:e.length;return o?Ko(e,(t=o-(t=n||t===r?1:ju(t)))<0?0:t,o):[]},yr.takeRightWhile=function(e,t){return e&&e.length?ni(e,ua(t,3),!1,!0):[]},yr.takeWhile=function(e,t){return e&&e.length?ni(e,ua(t,3)):[]},yr.tap=function(e,t){return t(e),e},yr.throttle=function(e,t,n){var r=!0,o=!0;if("function"!=typeof e)throw new nt(a);return pu(n)&&(r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o),Ns(e,t,{leading:r,maxWait:t,trailing:o})},yr.thru=Os,yr.toArray=bu,yr.toPairs=Yu,yr.toPairsIn=Xu,yr.toPath=function(e){return nu(e)?Xt(e,Za):wu(e)?[e]:Ri(za(Mu(e)))},yr.toPlainObject=Iu,yr.transform=function(e,t,n){var r=nu(e),o=r||au(e)||Cu(e);if(t=ua(t,4),null==n){var i=e&&e.constructor;n=o?r?new i:[]:pu(e)&&cu(i)?mr(Ct(e)):{}}return(o?Bt:to)(e,function(e,r,o){return t(n,e,r,o)}),n},yr.unary=function(e){return ks(e,1)},yr.union=hs,yr.unionBy=ds,yr.unionWith=_s,yr.uniq=function(e){return e&&e.length?Qo(e):[]},yr.uniqBy=function(e,t){return e&&e.length?Qo(e,ua(t,2)):[]},yr.uniqWith=function(e,t){return t="function"==typeof t?t:r,e&&e.length?Qo(e,r,t):[]},yr.unset=function(e,t){return null==e||ei(e,t)},yr.unzip=gs,yr.unzipWith=vs,yr.update=function(e,t,n){return null==e?e:ti(e,t,si(n))},yr.updateWith=function(e,t,n,o){return o="function"==typeof o?o:r,null==e?e:ti(e,t,si(n),o)},yr.values=$u,yr.valuesIn=function(e){return null==e?[]:wn(e,Du(e))},yr.without=ys,yr.words=cc,yr.wrap=function(e,t){return qs(si(t),e)},yr.xor=ms,yr.xorBy=ws,yr.xorWith=Cs,yr.zip=Rs,yr.zipObject=function(e,t){return ii(e||[],t||[],Jr)},yr.zipObjectDeep=function(e,t){return ii(e||[],t||[],Wo)},yr.zipWith=As,yr.entries=Yu,yr.entriesIn=Xu,yr.extend=Lu,yr.extendWith=Ju,mc(yr,yr),yr.add=Ic,yr.attempt=fc,yr.camelCase=Qu,yr.capitalize=ec,yr.ceil=Mc,yr.clamp=function(e,t,n){return n===r&&(n=t,t=r),n!==r&&(n=(n=Eu(n))==n?n:0),t!==r&&(t=(t=Eu(t))==t?t:0),Gr(Eu(e),t,n)},yr.clone=function(e){return Vr(e,p)},yr.cloneDeep=function(e){return Vr(e,f|p)},yr.cloneDeepWith=function(e,t){return Vr(e,f|p,t="function"==typeof t?t:r)},yr.cloneWith=function(e,t){return Vr(e,p,t="function"==typeof t?t:r)},yr.conformsTo=function(e,t){return null==t||Nr(e,t,Nu(t))},yr.deburr=tc,yr.defaultTo=function(e,t){return null==e||e!=e?t:e},yr.divide=Fc,yr.endsWith=function(e,t,n){e=Mu(e),t=$o(t);var o=e.length,i=n=n===r?o:Gr(ju(n),0,o);return(n-=t.length)>=0&&e.slice(n,i)==t},yr.eq=$s,yr.escape=function(e){return(e=Mu(e))&&Ae.test(e)?e.replace(Ce,jn):e},yr.escapeRegExp=function(e){return(e=Mu(e))&&Fe.test(e)?e.replace(Me,"\\$&"):e},yr.every=function(e,t,n){var o=nu(e)?zt:Zr;return n&&Oa(e,t,n)&&(t=r),o(e,ua(t,3))},yr.find=Es,yr.findIndex=ts,yr.findKey=function(e,t){return an(e,ua(t,3),to)},yr.findLast=Is,yr.findLastIndex=ns,yr.findLastKey=function(e,t){return an(e,ua(t,3),no)},yr.floor=Lc,yr.forEach=Ms,yr.forEachRight=Fs,yr.forIn=function(e,t){return null==e?e:Qr(e,ua(t,3),Du)},yr.forInRight=function(e,t){return null==e?e:eo(e,ua(t,3),Du)},yr.forOwn=function(e,t){return e&&to(e,ua(t,3))},yr.forOwnRight=function(e,t){return e&&no(e,ua(t,3))},yr.get=Uu,yr.gt=Qs,yr.gte=eu,yr.has=function(e,t){return null!=e&&ya(e,t,uo)},yr.hasIn=ku,yr.head=os,yr.identity=_c,yr.includes=function(e,t,n,r){e=ou(e)?e:$u(e),n=n&&!r?ju(n):0;var o=e.length;return n<0&&(n=Yn(o+n,0)),mu(e)?n<=o&&e.indexOf(t,n)>-1:!!o&&un(e,t,n)>-1},yr.indexOf=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var o=null==n?0:ju(n);return o<0&&(o=Yn(r+o,0)),un(e,t,o)},yr.inRange=function(e,t,n){return t=Ou(t),n===r?(n=t,t=0):n=Ou(n),fo(e=Eu(e),t,n)},yr.invoke=Wu,yr.isArguments=tu,yr.isArray=nu,yr.isArrayBuffer=ru,yr.isArrayLike=ou,yr.isArrayLikeObject=iu,yr.isBoolean=function(e){return!0===e||!1===e||hu(e)&&ao(e)==W},yr.isBuffer=au,yr.isDate=su,yr.isElement=function(e){return hu(e)&&1===e.nodeType&&!gu(e)},yr.isEmpty=function(e){if(null==e)return!0;if(ou(e)&&(nu(e)||"string"==typeof e||"function"==typeof e.splice||au(e)||Cu(e)||tu(e)))return!e.length;var t=_a(e);if(t==Z||t==ee)return!e.size;if(Fa(e))return!Co(e).length;for(var n in e)if(ut.call(e,n))return!1;return!0},yr.isEqual=function(e,t){return go(e,t)},yr.isEqualWith=function(e,t,n){var o=(n="function"==typeof n?n:r)?n(e,t):r;return o===r?go(e,t,r,n):!!o},yr.isError=uu,yr.isFinite=function(e){return"number"==typeof e&&zn(e)},yr.isFunction=cu,yr.isInteger=fu,yr.isLength=lu,yr.isMap=du,yr.isMatch=function(e,t){return e===t||yo(e,t,fa(t))},yr.isMatchWith=function(e,t,n){return n="function"==typeof n?n:r,yo(e,t,fa(t),n)},yr.isNaN=function(e){return _u(e)&&e!=+e},yr.isNative=function(e){if(Ma(e))throw new Ye(i);return mo(e)},yr.isNil=function(e){return null==e},yr.isNull=function(e){return null===e},yr.isNumber=_u,yr.isObject=pu,yr.isObjectLike=hu,yr.isPlainObject=gu,yr.isRegExp=vu,yr.isSafeInteger=function(e){return fu(e)&&e>=-L&&e<=L},yr.isSet=yu,yr.isString=mu,yr.isSymbol=wu,yr.isTypedArray=Cu,yr.isUndefined=function(e){return e===r},yr.isWeakMap=function(e){return hu(e)&&_a(e)==oe},yr.isWeakSet=function(e){return hu(e)&&ao(e)==ie},yr.join=function(e,t){return null==e?"":Zn.call(e,t)},yr.kebabCase=nc,yr.last=us,yr.lastIndexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var i=o;return n!==r&&(i=(i=ju(n))<0?Yn(o+i,0):Xn(i,o-1)),t==t?Un(e,t,i):sn(e,fn,i,!0)},yr.lowerCase=rc,yr.lowerFirst=oc,yr.lt=Ru,yr.lte=Au,yr.max=function(e){return e&&e.length?qr(e,_c,so):r},yr.maxBy=function(e,t){return e&&e.length?qr(e,ua(t,2),so):r},yr.mean=function(e){return ln(e,_c)},yr.meanBy=function(e,t){return ln(e,ua(t,2))},yr.min=function(e){return e&&e.length?qr(e,_c,Ao):r},yr.minBy=function(e,t){return e&&e.length?qr(e,ua(t,2),Ao):r},yr.stubArray=Pc,yr.stubFalse=Ec,yr.stubObject=function(){return{}},yr.stubString=function(){return""},yr.stubTrue=function(){return!0},yr.multiply=Jc,yr.nth=function(e,t){return e&&e.length?Io(e,ju(t)):r},yr.noConflict=function(){return Ft._===this&&(Ft._=ht),this},yr.noop=wc,yr.now=Us,yr.pad=function(e,t,n){e=Mu(e);var r=(t=ju(t))?kn(e):0;if(!t||r>=t)return e;var o=(t-r)/2;return Vi(Wn(o),n)+e+Vi(Hn(o),n)},yr.padEnd=function(e,t,n){e=Mu(e);var r=(t=ju(t))?kn(e):0;return t&&r<t?e+Vi(t-r,n):e},yr.padStart=function(e,t,n){e=Mu(e);var r=(t=ju(t))?kn(e):0;return t&&r<t?Vi(t-r,n)+e:e},yr.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),Qn(Mu(e).replace(Je,""),t||0)},yr.random=function(e,t,n){if(n&&"boolean"!=typeof n&&Oa(e,t,n)&&(t=n=r),n===r&&("boolean"==typeof t?(n=t,t=r):"boolean"==typeof e&&(n=e,e=r)),e===r&&t===r?(e=0,t=1):(e=Ou(e),t===r?(t=e,e=0):t=Ou(t)),e>t){var o=e;e=t,t=o}if(n||e%1||t%1){var i=er();return Xn(e+i*(t-e+Pt("1e-"+((i+"").length-1))),t)}return So(e,t)},yr.reduce=function(e,t,n){var r=nu(e)?Qt:dn,o=arguments.length<3;return r(e,ua(t,4),n,o,Kr)},yr.reduceRight=function(e,t,n){var r=nu(e)?en:dn,o=arguments.length<3;return r(e,ua(t,4),n,o,zr)},yr.repeat=function(e,t,n){return t=(n?Oa(e,t,n):t===r)?1:ju(t),Uo(Mu(e),t)},yr.replace=function(){var e=arguments,t=Mu(e[0]);return e.length<3?t:t.replace(e[1],e[2])},yr.result=function(e,t,n){var o=-1,i=(t=ui(t,e)).length;for(i||(i=1,e=r);++o<i;){var a=null==e?r:e[Za(t[o])];a===r&&(o=i,a=n),e=cu(a)?a.call(e):a}return e},yr.round=Tc,yr.runInContext=e,yr.sample=function(e){return(nu(e)?Ir:Go)(e)},yr.size=function(e){if(null==e)return 0;if(ou(e))return mu(e)?kn(e):e.length;var t=_a(e);return t==Z||t==ee?e.size:Co(e).length},yr.snakeCase=ic,yr.some=function(e,t,n){var o=nu(e)?tn:zo;return n&&Oa(e,t,n)&&(t=r),o(e,ua(t,3))},yr.sortedIndex=function(e,t){return Zo(e,t)},yr.sortedIndexBy=function(e,t,n){return qo(e,t,ua(n,2))},yr.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var r=Zo(e,t);if(r<n&&$s(e[r],t))return r}return-1},yr.sortedLastIndex=function(e,t){return Zo(e,t,!0)},yr.sortedLastIndexBy=function(e,t,n){return qo(e,t,ua(n,2),!0)},yr.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Zo(e,t,!0)-1;if($s(e[n],t))return n}return-1},yr.startCase=ac,yr.startsWith=function(e,t,n){return e=Mu(e),n=null==n?0:Gr(ju(n),0,e.length),t=$o(t),e.slice(n,n+t.length)==t},yr.subtract=xc,yr.sum=function(e){return e&&e.length?gn(e,_c):0},yr.sumBy=function(e,t){return e&&e.length?gn(e,ua(t,2)):0},yr.template=function(e,t,n){var o=yr.templateSettings;n&&Oa(e,t,n)&&(t=r),e=Mu(e),t=Ju({},t,o,Yi);var i,a,s=Ju({},t.imports,o.imports,Yi),u=Nu(s),c=wn(s,u),f=0,l=t.interpolate||Ze,p="__p += '",h=et((t.escape||Ze).source+"|"+l.source+"|"+(l===je?Ge:Ze).source+"|"+(t.evaluate||Ze).source+"|$","g"),d="//# sourceURL="+("sourceURL"in t?t.sourceURL:"lodash.templateSources["+ ++At+"]")+"\n";e.replace(h,function(t,n,r,o,s,u){return r||(r=o),p+=e.slice(f,u).replace(qe,Pn),n&&(i=!0,p+="' +\n__e("+n+") +\n'"),s&&(a=!0,p+="';\n"+s+";\n__p += '"),r&&(p+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),f=u+t.length,t}),p+="';\n";var _=t.variable;_||(p="with (obj) {\n"+p+"\n}\n"),p=(a?p.replace(ve,""):p).replace(ye,"$1").replace(me,"$1;"),p="function("+(_||"obj")+") {\n"+(_?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+p+"return __p\n}";var g=fc(function(){return Xe(u,d+"return "+p).apply(r,c)});if(g.source=p,uu(g))throw g;return g},yr.times=function(e,t){if((e=ju(e))<1||e>L)return[];var n=x,r=Xn(e,x);t=ua(t),e-=x;for(var o=vn(r,t);++n<e;)t(n);return o},yr.toFinite=Ou,yr.toInteger=ju,yr.toLength=Pu,yr.toLower=function(e){return Mu(e).toLowerCase()},yr.toNumber=Eu,yr.toSafeInteger=function(e){return e?Gr(ju(e),-L,L):0===e?e:0},yr.toString=Mu,yr.toUpper=function(e){return Mu(e).toUpperCase()},yr.trim=function(e,t,n){if((e=Mu(e))&&(n||t===r))return e.replace(Le,"");if(!e||!(t=$o(t)))return e;var o=Gn(e),i=Gn(t);return fi(o,Rn(o,i),An(o,i)+1).join("")},yr.trimEnd=function(e,t,n){if((e=Mu(e))&&(n||t===r))return e.replace(Te,"");if(!e||!(t=$o(t)))return e;var o=Gn(e);return fi(o,0,An(o,Gn(t))+1).join("")},yr.trimStart=function(e,t,n){if((e=Mu(e))&&(n||t===r))return e.replace(Je,"");if(!e||!(t=$o(t)))return e;var o=Gn(e);return fi(o,Rn(o,Gn(t))).join("")},yr.truncate=function(e,t){var n=O,o=j;if(pu(t)){var i="separator"in t?t.separator:i;n="length"in t?ju(t.length):n,o="omission"in t?$o(t.omission):o}var a=(e=Mu(e)).length;if(In(e)){var s=Gn(e);a=s.length}if(n>=a)return e;var u=n-kn(o);if(u<1)return o;var c=s?fi(s,0,u).join(""):e.slice(0,u);if(i===r)return c+o;if(s&&(u+=c.length-u),vu(i)){if(e.slice(u).search(i)){var f,l=c;for(i.global||(i=et(i.source,Mu(Ve.exec(i))+"g")),i.lastIndex=0;f=i.exec(l);)var p=f.index;c=c.slice(0,p===r?u:p)}}else if(e.indexOf($o(i),u)!=u){var h=c.lastIndexOf(i);h>-1&&(c=c.slice(0,h))}return c+o},yr.unescape=function(e){return(e=Mu(e))&&Re.test(e)?e.replace(we,Vn):e},yr.uniqueId=function(e){var t=++ct;return Mu(e)+t},yr.upperCase=sc,yr.upperFirst=uc,yr.each=Ms,yr.eachRight=Fs,yr.first=os,mc(yr,function(){var e={};return to(yr,function(t,n){ut.call(yr.prototype,n)||(e[n]=t)}),e}(),{chain:!1}),yr.VERSION="4.17.11",Bt(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){yr[e].placeholder=yr}),Bt(["drop","take"],function(e,t){Rr.prototype[e]=function(n){n=n===r?1:Yn(ju(n),0);var o=this.__filtered__&&!t?new Rr(this):this.clone();return o.__filtered__?o.__takeCount__=Xn(n,o.__takeCount__):o.__views__.push({size:Xn(n,x),type:e+(o.__dir__<0?"Right":"")}),o},Rr.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),Bt(["filter","map","takeWhile"],function(e,t){var n=t+1,r=n==I||3==n;Rr.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:ua(e,3),type:n}),t.__filtered__=t.__filtered__||r,t}}),Bt(["head","last"],function(e,t){var n="take"+(t?"Right":"");Rr.prototype[e]=function(){return this[n](1).value()[0]}}),Bt(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");Rr.prototype[e]=function(){return this.__filtered__?new Rr(this):this[n](1)}}),Rr.prototype.compact=function(){return this.filter(_c)},Rr.prototype.find=function(e){return this.filter(e).head()},Rr.prototype.findLast=function(e){return this.reverse().find(e)},Rr.prototype.invokeMap=ko(function(e,t){return"function"==typeof e?new Rr(this):this.map(function(n){return ho(n,e,t)})}),Rr.prototype.reject=function(e){return this.filter(zs(ua(e)))},Rr.prototype.slice=function(e,t){e=ju(e);var n=this;return n.__filtered__&&(e>0||t<0)?new Rr(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==r&&(n=(t=ju(t))<0?n.dropRight(-t):n.take(t-e)),n)},Rr.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Rr.prototype.toArray=function(){return this.take(x)},to(Rr.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),o=/^(?:head|last)$/.test(t),i=yr[o?"take"+("last"==t?"Right":""):t],a=o||/^find/.test(t);i&&(yr.prototype[t]=function(){var t=this.__wrapped__,s=o?[1]:arguments,u=t instanceof Rr,c=s[0],f=u||nu(t),l=function(e){var t=i.apply(yr,$t([e],s));return o&&p?t[0]:t};f&&n&&"function"==typeof c&&1!=c.length&&(u=f=!1);var p=this.__chain__,h=!!this.__actions__.length,d=a&&!p,_=u&&!h;if(!a&&f){t=_?t:new Rr(this);var g=e.apply(t,s);return g.__actions__.push({func:Os,args:[l],thisArg:r}),new Cr(g,p)}return d&&_?e.apply(this,s):(g=this.thru(l),d?o?g.value()[0]:g.value():g)})}),Bt(["pop","push","shift","sort","splice","unshift"],function(e){var t=rt[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);yr.prototype[e]=function(){var e=arguments;if(r&&!this.__chain__){var o=this.value();return t.apply(nu(o)?o:[],e)}return this[n](function(n){return t.apply(nu(n)?n:[],e)})}}),to(Rr.prototype,function(e,t){var n=yr[t];if(n){var r=n.name+"";(cr[r]||(cr[r]=[])).push({name:t,func:n})}}),cr[Hi(r,g).name]=[{name:"wrapper",func:r}],Rr.prototype.clone=function(){var e=new Rr(this.__wrapped__);return e.__actions__=Ri(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=Ri(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=Ri(this.__views__),e},Rr.prototype.reverse=function(){if(this.__filtered__){var e=new Rr(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Rr.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=nu(e),r=t<0,o=n?e.length:0,i=ga(0,o,this.__views__),a=i.start,s=i.end,u=s-a,c=r?s:a-1,f=this.__iteratees__,l=f.length,p=0,h=Xn(u,this.__takeCount__);if(!n||!r&&o==u&&h==u)return ri(e,this.__actions__);var d=[];e:for(;u--&&p<h;){for(var _=-1,g=e[c+=t];++_<l;){var v=f[_],y=v.iteratee,m=v.type,w=y(g);if(m==M)g=w;else if(!w){if(m==I)continue e;break e}}d[p++]=g}return d},yr.prototype.at=js,yr.prototype.chain=function(){return bs(this)},yr.prototype.commit=function(){return new Cr(this.value(),this.__chain__)},yr.prototype.next=function(){this.__values__===r&&(this.__values__=bu(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?r:this.__values__[this.__index__++]}},yr.prototype.plant=function(e){for(var t,n=this;n instanceof wr;){var o=Xa(n);o.__index__=0,o.__values__=r,t?i.__wrapped__=o:t=o;var i=o;n=n.__wrapped__}return i.__wrapped__=e,t},yr.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof Rr){var t=e;return this.__actions__.length&&(t=new Rr(this)),(t=t.reverse()).__actions__.push({func:Os,args:[ps],thisArg:r}),new Cr(t,this.__chain__)}return this.thru(ps)},yr.prototype.toJSON=yr.prototype.valueOf=yr.prototype.value=function(){return ri(this.__wrapped__,this.__actions__)},yr.prototype.first=yr.prototype.head,Jt&&(yr.prototype[Jt]=function(){return this}),yr}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(Ft._=Bn,define(function(){return Bn})):Jt?((Jt.exports=Bn)._=Bn,Lt._=Bn):Ft._=Bn}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],7:[function(e,t,n){(function(){var n=e("crypt"),r=e("charenc").utf8,o=e("is-buffer"),i=e("charenc").bin,a=function(e,t){e.constructor==String?e=t&&"binary"===t.encoding?i.stringToBytes(e):r.stringToBytes(e):o(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||(e=e.toString());for(var s=n.bytesToWords(e),u=8*e.length,c=1732584193,f=-271733879,l=-1732584194,p=271733878,h=0;h<s.length;h++)s[h]=16711935&(s[h]<<8|s[h]>>>24)|4278255360&(s[h]<<24|s[h]>>>8);s[u>>>5]|=128<<u%32,s[14+(u+64>>>9<<4)]=u;var d=a._ff,_=a._gg,g=a._hh,v=a._ii;for(h=0;h<s.length;h+=16){var y=c,m=f,w=l,C=p;f=v(f=v(f=v(f=v(f=g(f=g(f=g(f=g(f=_(f=_(f=_(f=_(f=d(f=d(f=d(f=d(f,l=d(l,p=d(p,c=d(c,f,l,p,s[h+0],7,-680876936),f,l,s[h+1],12,-389564586),c,f,s[h+2],17,606105819),p,c,s[h+3],22,-1044525330),l=d(l,p=d(p,c=d(c,f,l,p,s[h+4],7,-176418897),f,l,s[h+5],12,1200080426),c,f,s[h+6],17,-1473231341),p,c,s[h+7],22,-45705983),l=d(l,p=d(p,c=d(c,f,l,p,s[h+8],7,1770035416),f,l,s[h+9],12,-1958414417),c,f,s[h+10],17,-42063),p,c,s[h+11],22,-1990404162),l=d(l,p=d(p,c=d(c,f,l,p,s[h+12],7,1804603682),f,l,s[h+13],12,-40341101),c,f,s[h+14],17,-1502002290),p,c,s[h+15],22,1236535329),l=_(l,p=_(p,c=_(c,f,l,p,s[h+1],5,-165796510),f,l,s[h+6],9,-1069501632),c,f,s[h+11],14,643717713),p,c,s[h+0],20,-373897302),l=_(l,p=_(p,c=_(c,f,l,p,s[h+5],5,-701558691),f,l,s[h+10],9,38016083),c,f,s[h+15],14,-660478335),p,c,s[h+4],20,-405537848),l=_(l,p=_(p,c=_(c,f,l,p,s[h+9],5,568446438),f,l,s[h+14],9,-1019803690),c,f,s[h+3],14,-187363961),p,c,s[h+8],20,1163531501),l=_(l,p=_(p,c=_(c,f,l,p,s[h+13],5,-1444681467),f,l,s[h+2],9,-51403784),c,f,s[h+7],14,1735328473),p,c,s[h+12],20,-1926607734),l=g(l,p=g(p,c=g(c,f,l,p,s[h+5],4,-378558),f,l,s[h+8],11,-2022574463),c,f,s[h+11],16,1839030562),p,c,s[h+14],23,-35309556),l=g(l,p=g(p,c=g(c,f,l,p,s[h+1],4,-1530992060),f,l,s[h+4],11,1272893353),c,f,s[h+7],16,-155497632),p,c,s[h+10],23,-1094730640),l=g(l,p=g(p,c=g(c,f,l,p,s[h+13],4,681279174),f,l,s[h+0],11,-358537222),c,f,s[h+3],16,-722521979),p,c,s[h+6],23,76029189),l=g(l,p=g(p,c=g(c,f,l,p,s[h+9],4,-640364487),f,l,s[h+12],11,-421815835),c,f,s[h+15],16,530742520),p,c,s[h+2],23,-995338651),l=v(l,p=v(p,c=v(c,f,l,p,s[h+0],6,-198630844),f,l,s[h+7],10,1126891415),c,f,s[h+14],15,-1416354905),p,c,s[h+5],21,-57434055),l=v(l,p=v(p,c=v(c,f,l,p,s[h+12],6,1700485571),f,l,s[h+3],10,-1894986606),c,f,s[h+10],15,-1051523),p,c,s[h+1],21,-2054922799),l=v(l,p=v(p,c=v(c,f,l,p,s[h+8],6,1873313359),f,l,s[h+15],10,-30611744),c,f,s[h+6],15,-1560198380),p,c,s[h+13],21,1309151649),l=v(l,p=v(p,c=v(c,f,l,p,s[h+4],6,-145523070),f,l,s[h+11],10,-1120210379),c,f,s[h+2],15,718787259),p,c,s[h+9],21,-343485551),c=c+y>>>0,f=f+m>>>0,l=l+w>>>0,p=p+C>>>0}return n.endian([c,f,l,p])};a._ff=function(e,t,n,r,o,i,a){var s=e+(t&n|~t&r)+(o>>>0)+a;return(s<<i|s>>>32-i)+t},a._gg=function(e,t,n,r,o,i,a){var s=e+(t&r|n&~r)+(o>>>0)+a;return(s<<i|s>>>32-i)+t},a._hh=function(e,t,n,r,o,i,a){var s=e+(t^n^r)+(o>>>0)+a;return(s<<i|s>>>32-i)+t},a._ii=function(e,t,n,r,o,i,a){var s=e+(n^(t|~r))+(o>>>0)+a;return(s<<i|s>>>32-i)+t},a._blocksize=16,a._digestsize=16,t.exports=function(e,t){if(void 0===e||null===e)throw new Error("Illegal argument "+e);var r=n.wordsToBytes(a(e,t));return t&&t.asBytes?r:t&&t.asString?i.bytesToString(r):n.bytesToHex(r)}})()},{charenc:3,crypt:4,"is-buffer":5}],8:[function(e,t,n){var r,o,i=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function u(e){if(r===setTimeout)return setTimeout(e,0);if((r===a||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}function c(e){if(o===clearTimeout)return clearTimeout(e);if((o===s||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{return o(e)}catch(t){try{return o.call(null,e)}catch(t){return o.call(this,e)}}}(function(){try{r="function"==typeof setTimeout?setTimeout:a}catch(e){r=a}try{o="function"==typeof clearTimeout?clearTimeout:s}catch(e){o=s}})();var f,l=[],p=!1,h=-1;function d(){p&&f&&(p=!1,f.length?l=f.concat(l):h=-1,l.length&&_())}function _(){if(!p){var e=u(d);p=!0;for(var t=l.length;t;){for(f=l,l=[];++h<t;)f&&f[h].run();h=-1,t=l.length}f=null,p=!1,c(e)}}function g(e,t){this.fun=e,this.array=t}function v(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new g(e,t)),1!==l.length||p||u(_)},g.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],DismissTableEvent:[function(e,t,n){"use strict";cc._RF.push(t,"185f9WIuxtAs7bEWdjloWmU","DismissTableEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.mainType=2,t.subType=3,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],DismissVoteEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"98f17cD2ldOV4Ifm1IQutYc","DismissVoteEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("dismiss_vote",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],DismissVoteEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"21bd1IxHp1EWbOw4qTWo6hw","DismissVoteEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],DismissVoteTableEvent:[function(e,t,n){"use strict";cc._RF.push(t,"78639sPZXpInrC5BKyNAFBs","DismissVoteTableEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t){var n=e.call(this)||this;return n.mainType=2,n.subType=9,n.agree=t,n}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],FireKit:[function(e,t,n){"use strict";cc._RF.push(t,"75c871UmTpLjpK6atYRj6Hj","FireKit"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("./OnFire"),o=function(){function e(){}return e.init=function(t){null==e.fireDict[t]&&(e.fireDict[t]=new r.default)},e.use=function(t){return e.fireDict[t]},e.fireDict={},e}();n.default=o,cc._RF.pop()},{"./OnFire":"OnFire"}],GameServiceManager:[function(e,t,n){"use strict";cc._RF.push(t,"b2dc1/0dMtPtZy0HabQ6zpy","GameServiceManager"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("lodash"),o=function(){function e(){}return e.getInst=function(){return null==e.inst&&(e.inst=new e),e.inst},e.prototype.initGameService=function(e){this.serviceItems=e},e.prototype.getGameService=function(e){return r.filter(this.serviceItems,{name:e})},e.prototype.getGameServiceByServiceId=function(e){return r.find(this.serviceItems,{serviceId:e})},e.prototype.randomGameService=function(e){var t=r.filter(this.serviceItems,{name:e});return 0==t.length?null:r.shuffle(t)[0]},e}();n.default=o;cc._RF.pop()},{lodash:6}],HeroEnterEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"9262bUVcJ1OMrncc71qlLLd","HeroEnterEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_enter",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroEnterEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"6557bWuuOZGEaj7f/o9w4ew","HeroEnterEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroLeaveEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"59312p2OLVMVpxrIa2DeHgr","HeroLeaveEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_leave",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroLeaveEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"896faIVO4lN17aVbV+D8xoP","HeroLeaveEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroManager:[function(e,t,n){"use strict";cc._RF.push(t,"bf0a6u1ii9AXrLaSJD1Gaqa","HeroManager"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(){this.heroes={}}return e.getInst=function(){return null==e.inst&&(e.inst=new e),e.inst},e.prototype.getHero=function(e){return this.heroes[e]},e.prototype.addHero=function(e){this.heroes[e.userId]=e},e.prototype.setMe=function(e){this.addHero(e),this._me=e},e.prototype.getMe=function(){return this._me},e}();n.default=r,cc._RF.pop()},{}],HeroOfflineEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"cc232+JEc9IXbgRURJmYipn","HeroOfflineEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_offline",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroOfflineEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"1a7e34baSxGgJCeOXRk/Pim","HeroOfflineEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroOnlineEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"2bdcb4akB5PCIDM60Gtql4g","HeroOnlineEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_online",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroOnlineEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"4c020l2/ghJm64BE+cEUUNu","HeroOnlineEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroProfileEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"8398dN+QoNMJZuGkjs1WeW8","HeroProfileEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_profile",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroProfileEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"f4b52Qov3VIar4WJEvMWsFc","HeroProfileEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroProfileEvent:[function(e,t,n){"use strict";cc._RF.push(t,"d26e8iNpoJIG5rmQRuBNG6h","HeroProfileEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t){var n=e.call(this)||this;return n.mainType=2,n.subType=6,n.userId=t,n}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroSceneResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"bf9a3GjD+lFpbalV5Ux7wLj","HeroSceneResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_scene",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroSceneResponse:[function(e,t,n){"use strict";cc._RF.push(t,"bd4fbg6viBFxLkJAUT7kzlq","HeroSceneResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o;var i=function(){return function(){}}();n.HeroItem=i,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroSitDownEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"1315cKkiT9PL5X4OgpjKilw","HeroSitDownEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_sitDown",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroSitDownEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"ed87dB8W6NFmK16uagR4DDS","HeroSitDownEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],HeroStandUpEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"1f024N+AvpNdJhzaVKvJ3EU","HeroStandUpEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("hero_standUp",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],HeroStandUpEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"74f61ofS2FIwI523dkqOntK","HeroStandUpEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],Hero:[function(e,t,n){"use strict";cc._RF.push(t,"b9b294yHEFEZ7636zwoW9o7","Hero"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AppConfig"),o=function(){return function(e,t,n,o,i,a,s,u,c,f,l,p){this.userName=e,this.showId=t,this.userId=n,this.nickName=o,this.gender=i,this.avatar=cc.sys.isBrowser?r.default.AVATAR_PROXY_URL+"?url="+encodeURIComponent(a):a,this.distributorId=s,this.address=u,this.longitude=c,this.latitude=f,this.ip=l,this.certStatus=p}}();n.default=o,cc._RF.pop()},{"../AppConfig":"AppConfig"}],HotUpdateManager:[function(e,t,n){"use strict";cc._RF.push(t,"afd03NcMeNAfbZBiq5ALqlY","HotUpdateManager"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AlertWindow"),o=function(){function e(){this._updating=!1,this._canRetry=!1,this._storagePath=(jsb.fileUtils?jsb.fileUtils.getWritablePath():"/")+"remote-asset",cc.log("storagePath:"+this._storagePath),this._am=new jsb.AssetsManager("",this._storagePath,function(e,t){cc.log("JS Custom Version Compare: version A is "+e+", version B is "+t);for(var n=e.split("."),r=t.split("."),o=0;o<n.length;++o){var i=parseInt(n[o]),a=parseInt(r[o]||0);if(i!=a)return i-a}return r.length>n.length?-1:0}),cc.sys.os==cc.sys.OS_ANDROID&&this._am.setMaxConcurrentTask(2)}return e.getInst=function(){return null==e.inst&&(e.inst=new e),e.inst},e.prototype.checkAndUpdate=function(e){var t=this;this._updating?cc.log("Checking or updating ..."):(this._callback=e,this._am.getState()===jsb.AssetsManager.State.UNINITED&&(cc.log("load project.manifest"),cc.loader.loadRes("project",function(e,n){cc.log(n._$nativeAsset);var o=new jsb.Manifest(n._$nativeAsset,t._storagePath);if(t._am.loadLocalManifest(o,t._storagePath),!t._am.getLocalManifest()||!t._am.getLocalManifest().isLoaded())return cc.log("Failed to load local manifest ..."),void r.default.alert("\u51fa\u9519\u4e86","\u52a0\u8f7d\u672c\u5730\u7684manifest\u6587\u4ef6\u5931\u8d25,\u8bf7\u91cd\u65b0\u5b89\u88c5\u5ba2\u6237\u7aef.");t._am.setEventCallback(t.checkCb.bind(t)),t._am.checkUpdate(),t._updating=!0})))},e.prototype.checkCb=function(e){switch(cc.log("checkCb Code: "+e.getEventCode()),e.getEventCode()){case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:cc.log("No local manifest file found, hot update skipped."),r.default.alert("\u51fa\u9519\u4e86","\u52a0\u8f7d\u672c\u5730\u7684manifest\u6587\u4ef6\u5931\u8d25,\u66f4\u65b0\u5931\u8d25.");break;case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:cc.log("Fail to download manifest file, hot update skipped."),r.default.alert("\u51fa\u9519\u4e86","\u4e0b\u8f7dmanifest\u6587\u4ef6\u5931\u8d25,\u66f4\u65b0\u5931\u8d25.");break;case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:cc.log("Already up to date with the latest remote version.");break;case jsb.EventAssetsManager.NEW_VERSION_FOUND:cc.log("New version found, please try to update."),this._callback({code:0}),this._am.setEventCallback(this.updateCb.bind(this)),this._am.update(),this._updating=!0,cc.log("call update.");break;default:return}this._updating=!1},e.prototype.updateCb=function(e){var t=!1,n=!1;switch(cc.log("updateCb Code: "+e.getEventCode()),e.getEventCode()){case jsb.EventAssetsManager.ERROR_NO_LOCAL_MANIFEST:cc.log("No local manifest file found, hot update skipped."),r.default.alert("\u51fa\u9519\u4e86","\u52a0\u8f7d\u672c\u5730\u7684manifest\u6587\u4ef6\u5931\u8d25,\u66f4\u65b0\u5931\u8d25."),n=!0;break;case jsb.EventAssetsManager.UPDATE_PROGRESSION:this._callback({code:1,downloaded:e.getDownloadedFiles(),total:e.getTotalFiles()}),cc.log("downloaded:"+e.getDownloadedFiles()+" / "+e.getTotalFiles());var o=e.getMessage();o&&cc.log("Updated file: "+o);break;case jsb.EventAssetsManager.ERROR_DOWNLOAD_MANIFEST:case jsb.EventAssetsManager.ERROR_PARSE_MANIFEST:cc.log("Fail to download manifest file, hot update skipped."),n=!0;break;case jsb.EventAssetsManager.ALREADY_UP_TO_DATE:cc.log("Already up to date with the latest remote version."),n=!0;break;case jsb.EventAssetsManager.UPDATE_FINISHED:cc.log("Update finished. "+e.getMessage()),this._callback({code:2}),t=!0;break;case jsb.EventAssetsManager.UPDATE_FAILED:cc.log("Update failed. "+e.getMessage()),this._updating=!1,this._canRetry=!0;break;case jsb.EventAssetsManager.ERROR_UPDATING:cc.log("Asset update error: "+e.getAssetId()+", "+e.getMessage());break;case jsb.EventAssetsManager.ERROR_DECOMPRESS:cc.log(e.getMessage())}if(n&&(this._am.setEventCallback(null),this._updating=!1),t){this._am.setEventCallback(null);var i=jsb.fileUtils.getSearchPaths(),a=this._am.getLocalManifest().getSearchPaths();console.log(JSON.stringify(a)),Array.prototype.unshift.apply(i,a),cc.sys.localStorage.setItem("HotUpdateSearchPaths",JSON.stringify(i)),jsb.fileUtils.setSearchPaths(i),cc.audioEngine.stopAll(),cc.game.restart()}},e}();n.default=o,cc._RF.pop()},{"../AlertWindow":"AlertWindow"}],JoinTableEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"236b75l/31JOr5fUmWGNVAT","JoinTableEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).emit("join_table_success",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],JoinTableEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"6b3a2BgaD1O+4Pu41bDMNOz","JoinTableEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],JoinTableEvent:[function(e,t,n){"use strict";cc._RF.push(t,"96e52hvEaNMRq37PpKhk/p4","JoinTableEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t){var n=e.call(this)||this;return n.mainType=2,n.subType=2,n.tableNo=t,n}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],LeaveTableEvent:[function(e,t,n){"use strict";cc._RF.push(t,"662cfPYS5xAiaK1Hj10Kqty","LeaveTableEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.mainType=2,t.subType=4,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],LoadingWindow:[function(e,t,n){"use strict";cc._RF.push(t,"48b6aWYWtJFbqwUNS9Ocmq1","LoadingWindow");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return e.call(this)||this}return r(t,e),t.getInst=function(){return null==t.inst&&(t.inst=new t),t.inst},t.loading=function(e){var n=t.getInst();n.show(),n.contentPane.getChild("content").asTextField.text=e},t.close=function(){t.getInst().hide()},t.prototype.onInit=function(){this.contentPane=fgui.UIPackage.createObject("commons","LoadingWindow").asCom,this.center()},t}(fgui.Window);n.default=o,cc._RF.pop()},{}],LoginNotifyResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"382bfzcpttAo77TKGUOz3MF","LoginNotifyResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){},t}(e("../../ws/AiJ").AiJ.ResponseHandler);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],MahjongAction:[function(e,t,n){"use strict";cc._RF.push(t,"c4eeaGnW6ZIz7J1BnEFGHKQ","MahjongAction"),Object.defineProperty(n,"__esModule",{value:!0}),function(e){e.DISPATCH="DISPATCH",e.NOTIFY="NOTIFY",e.OUT="OUT",e.N="N",e.P="P",e.G="G",e.H="H"}(n.MahjongAction||(n.MahjongAction={})),cc._RF.pop()},{}],MahjongDispatchCardEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"8da7cuazHBCP7B9QfngOeiq","MahjongDispatchCardEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongDispathCardResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"5c900vWz6tNOoFimNnuWI30","MahjongDispathCardResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("dispatch_card",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongEndEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"45cc55MTndMYJbMJanf4NTX","MahjongEndEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("end",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongEndEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"b5c8cDoRlhG07kOj3U+UHbr","MahjongEndEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongErrorEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"6d061BqslFJUYyW+tt33Pjy","MahjongErrorEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){},t}(e("../../../ws/AiJ").AiJ.ResponseHandler);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongErrorEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"30778JeTGJHVahZP79qBdq7","MahjongErrorEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return e.call(this)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongGameActionRecord:[function(e,t,n){"use strict";cc._RF.push(t,"bc744Db9kBAMZ1SizdWYGnh","MahjongGameActionRecord"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){return function(){}}();n.default=r,cc._RF.pop()},{}],MahjongGameEndEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"41d60qY7pBKOYrdD6RzRfoI","MahjongGameEndEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("game_end",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongGameEndEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"4876a6Vz4dDpKTlU5tfogX7","MahjongGameEndEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongGameEngine:[function(e,t,n){"use strict";cc._RF.push(t,"f59f6utdDZDCKeTFD2TiC8Q","MahjongGameEngine"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../fire/FireKit"),o=e("../../AppConfig"),i=e("../../ws/AiJKit"),a=e("../event/ClientReadyEvent"),s=e("lodash"),u=e("./struct/MahjongWeaveItem"),c=e("./struct/MahjongWeaveType"),f=e("../response/HeroSceneResponse"),l=e("../../hero/HeroManager"),p=e("../event/HeroProfileEvent"),h=e("../../hero/Hero"),d=e("../AbstractRoomConfig"),_=function(){return function(e,t,n,r){this.chair=e,this.online=t,this.sitDown=n,this.nickName=r}}();n.HeroMate=_;var g=function(){function e(e,t){var n=this;this._heroMap={},this._weavesMap={},this._discardCardsMap={},this._meChair=-1,this._chairCount=4,this._leftCardCount=0,this._totalNumber=0,this._currentNumber=0,this._clientReady=!1,this._gameStart=!1,this.clientReady=function(){n._clientReady=!0,i.default.use(o.default.GAME_WS_NAME).send(new a.default)},this.dismissVoteCb=function(e){n._gameLayer.renderDismissVote(e.status,e.agrees,e.refuses,e.voteTime,e.countDown,n._meChair)},this.gameHeroOfflineCb=function(e){var t=n._heroMap[e.userId];t.online=!1,n.renderOnline(t)},this.gameHeroOnlineCb=function(e){var t=n._heroMap[e.userId];t.online=!0,n.renderOnline(t)},this.gameHeroStandUpCb=function(e){var t=n._heroMap[e.userId];t.sitDown=!1,n.renderSitDown(t)},this.gameHeroSitDownCb=function(e){var t=n._heroMap[e.userId];t.sitDown=!0,n.renderSitDown(t)},this.gameHeroEnterCb=function(e){n.heroEnter(e.userId,e.chair,e.nickName)},this.gameHeroLeaveCb=function(e){var t=n._heroMap[e.userId];n.renderLeave(t),delete n._heroMap[e.userId]},this.gameHeroSceneCb=function(e){var t=new f.HeroItem;t.userId=l.default.getInst().getMe().userId;var r=s.find(e.heroes,t);null!=r&&(n._meChair=r.chair),-1!=n._meChair&&s.each(e.heroes,function(e){n._heroMap[e.userId]=new _(e.chair,e.online,e.sitDown,e.nickName),n.renderSitDown(n._heroMap[e.userId]),n.renderOnline(n._heroMap[e.userId]),n.getHeroProfile(e.userId),n._gameLayer.renderHeroSummary(e.chair,e.nickName)})},this.gameHeroProfileCb=function(e){var t=new h.default(e.userName,e.showId,e.userId,e.nickName,e.gender,e.avatar,e.distributorId,e.address,e.longitude,e.latitude,e.ip,e.certStatus);l.default.getInst().addHero(t),n.renderHeroProfile(t)},this.endCb=function(e){window.setTimeout(function(){for(var t=0;t<n._chairCount;t++){var r=l.default.getInst().getHero(n.getUserId(t));n._gameLayer.renderEnd(n.switchView(t),e.score[t],e.actionStatistics[t],e.startedTime,e.endedTime,e.tableNo,r.userId==n._joinTableEventResponse.ownerId,r.distributorId)}n._gameLayer.renderEndInfo(e.tableNo,e.startedTime,e.endedTime)},4e3)},this.gameStartCb=function(e){n._gameStart=!0,n._gameLayer.renderGameStart(),n._meChair=e.chair,n._chairCount=e.chairCount,n._cards=e.cards,n._leftCardCount=e.leftCardCount,n._totalNumber=e.totalNumber,n._currentNumber=e.currentNumber,n._gameLayer.renderLeftCardCount(n._leftCardCount),n._gameLayer.renderLeftNumber(n._totalNumber-n._currentNumber);for(var t=0;t<e.chairCount;t++){n._weavesMap[t]=[],n._discardCardsMap[t]=[],n.renderSitDown(new _(t,!0,!0,""));var r=n.switchView(t);switch(n._gameLayer.renderScore(r,e.scores[t]),r){case 0:n._gameLayer.renderSouthDiscardCard(n._discardCardsMap[t]),n._gameLayer.renderSouthCard(s.clone(n._cards),n.getWeaveItems(t));break;case 1:n._gameLayer.renderEastDiscardCard(n._discardCardsMap[t]),n._gameLayer.renderEastCard(n.getWeaveItems(t));break;case 2:n._gameLayer.renderNorthDiscardCard(n._discardCardsMap[t]),n._gameLayer.renderNorthCard(n.getWeaveItems(t));break;case 3:n._gameLayer.renderWestDiscardCard(n._discardCardsMap[t]),n._gameLayer.renderWestCard(n.getWeaveItems(t))}}},this.gamePlayingSceneCb=function(e){if(n._gameLayer.renderGameStart(),n._gameStart=!0,n._meChair=e.chair,n._chairCount=e.chairCount,n._cards=e.cards,n._leftCardCount=e.leftCardCount,n._totalNumber=e.totalNumber,n._currentNumber=e.currentNumber,e.current==n._meChair&&-1!=e.currCard){var t=n._cards.indexOf(e.currCard);n._cards.splice(t,1),n._cards.push(e.currCard),n._currCard=e.currCard}for(var r=0;r<e.chairCount;r++){n._discardCardsMap[r]=s.slice(e.discardCards[r],0,e.discardCount[r]),n._weavesMap[r]=e.weaveItems[r],n.renderSitDown(new _(r,!0,!0,""));var o=n.switchView(r);switch(n._gameLayer.renderScore(o,e.scores[r]),o){case 0:n._gameLayer.renderSouthDiscardCard(s.clone(n._discardCardsMap[r])),n._gameLayer.renderSouthCard(s.clone(n._cards),n.getWeaveItems(r),n._currCard);break;case 1:n._gameLayer.renderEastDiscardCard(s.clone(n._discardCardsMap[r])),n._gameLayer.renderEastCard(n.getWeaveItems(r),r==e.current?0:-1);break;case 2:n._gameLayer.renderNorthDiscardCard(s.clone(n._discardCardsMap[r])),n._gameLayer.renderNorthCard(n.getWeaveItems(r),r==e.current?0:-1);break;case 3:n._gameLayer.renderWestDiscardCard(s.clone(n._discardCardsMap[r])),n._gameLayer.renderWestCard(n.getWeaveItems(r),r==e.current?0:-1)}}n._gameLayer.renderLeftCardCount(n._leftCardCount),n._gameLayer.renderLeftNumber(n._totalNumber-n._currentNumber),n._gameLayer.renderPilotLamp(n.switchView(e.current)),0!=e.action&&n._gameLayer.renderOperateNotify(e.actionCard,0!=(4&e.action),0!=(2&e.action),0!=(1&e.action),!0,e.actionCards)},this.gameEndCb=function(e){for(var t=0;t<e.chairCount;t++){var r=-1!=s.indexOf(e.chairs,t)?e.currCard:-1;if(-1!=s.indexOf(e.chairs,e.provider)&&-1!=s.indexOf(e.chairs,t)){var o=e.cards[t].indexOf(r);e.cards[t].splice(o,1)}var i=s.clone(e.cards[t]);switch(-1!=s.indexOf(e.chairs,t)&&0==n.switchView(t)&&i.push(r),n.switchView(t)){case 0:n._gameLayer.renderSouthCard(i,e.weaveItems[t],r);break;case 1:n._gameLayer.renderEastCard(e.weaveItems[t],r,i);break;case 2:n._gameLayer.renderNorthCard(e.weaveItems[t],r,i);break;case 3:n._gameLayer.renderWestCard(e.weaveItems[t],r,i)}}window.setTimeout(function(){for(var t=0;t<e.chairCount;t++)n._gameLayer.renderGameEndCards(n.switchView(t),e.weaveItems[t],e.cards[t],-1!=s.indexOf(e.chairs,t),-1==s.indexOf(e.chairs,t)&&e.provider==t,e.currCard),n._gameLayer.renderGameEndFlag(n.switchView(t),e.infos[t],e.totalScores[t],e.scores[t],-1!=s.indexOf(e.chairs,t),e.banker==t)},2e3)},this.gameDispatchCardCb=function(e){var t=n.getWeaveItems(e.chair);switch(e.chair==n._meChair&&(n._cards.push(e.card),n._currCard=e.card),n.switchView(e.chair)){case 0:n._gameLayer.renderSouthCard(s.clone(n._cards),t,n._currCard);break;case 1:n._gameLayer.renderEastCard(t,0);break;case 2:n._gameLayer.renderNorthCard(t,0);break;case 3:n._gameLayer.renderWestCard(t,0)}n._gameLayer.renderLeftCardCount(--n._leftCardCount),n._gameLayer.renderDispatchCard(),n._gameLayer.renderPilotLamp(n.switchView(e.chair))},this.gameOutCardCb=function(e){var t=n.getWeaveItems(e.chair),r=n.getDiscardCards(e.chair);if(r.push(e.card),e.chair==n._meChair){var o=n._cards.indexOf(e.card);n._cards.splice(o,1),n._cards=s.sortBy(n._cards)}switch(n.switchView(e.chair)){case 0:n._gameLayer.renderSouthDiscardCard(r,!0),n._gameLayer.renderSouthCard(s.clone(n._cards),t);break;case 1:n._gameLayer.renderEastDiscardCard(r,!0),n._gameLayer.renderEastCard(t);break;case 2:n._gameLayer.renderNorthDiscardCard(r,!0),n._gameLayer.renderNorthCard(t);break;case 3:n._gameLayer.renderWestDiscardCard(r,!0),n._gameLayer.renderWestCard(t)}},this.gameOperateNotifyCb=function(e){n._meChair==e.chair&&n._gameLayer.renderOperateNotify(e.card,0!=(4&e.action),0!=(2&e.action),0!=(1&e.action),!0,e.cards),n._gameLayer.renderPilotLamp(-1)},this.gameOperateResultCb=function(e){var t=n.getWeaveItems(e.chair),r=n.getDiscardCards(e.provider),o=0;switch(e.action){case 0:break;case 1:n._meChair==e.chair&&s.remove(n._cards,function(t){return t==e.card&&o++<2}),e.chair!=e.provider&&(r=s.dropRight(r,1),n._discardCardsMap[e.provider]=r),t.push(new u.default(c.MahjongWeaveType.P,e.card,!0,e.provider));break;case 2:var i=s.find(t,{centerCard:e.card,weaveType:c.MahjongWeaveType.P});n._meChair==e.chair&&s.remove(n._cards,function(t){return t==e.card&&o++<(null==i?e.provider!=e.chair?3:4:1)}),e.chair!=e.provider&&(r=s.dropRight(r,1),n._discardCardsMap[e.provider]=r),null==i?t.push(new u.default(c.MahjongWeaveType.G,e.card,e.provider!=e.chair,e.provider)):i.weaveType=c.MahjongWeaveType.G}switch(n.switchView(e.chair)){case 0:n._gameLayer.renderSouthCard(s.clone(n._cards),t);break;case 1:n._gameLayer.renderEastCard(t);break;case 2:n._gameLayer.renderNorthCard(t);break;case 3:n._gameLayer.renderWestCard(t)}switch(n.switchView(e.provider)){case 0:n._gameLayer.renderSouthDiscardCard(r);break;case 1:n._gameLayer.renderEastDiscardCard(r);break;case 2:n._gameLayer.renderNorthDiscardCard(r);break;case 3:n._gameLayer.renderWestDiscardCard(r)}n._gameLayer.renderPilotLamp(n.switchView(e.chair))},this.gameStatusCb=function(e){},this._gameLayer=e,this._joinTableEventResponse=t,r.default.use(o.default.GAME_FIRE).onGroup("game_start",this.gameStartCb,this),r.default.use(o.default.GAME_FIRE).onGroup("game_status",this.gameStatusCb,this),r.default.use(o.default.GAME_FIRE).onGroup("playing_scene",this.gamePlayingSceneCb,this),r.default.use(o.default.GAME_FIRE).onGroup("game_end",this.gameEndCb,this),r.default.use(o.default.GAME_FIRE).onGroup("dispatch_card",this.gameDispatchCardCb,this),r.default.use(o.default.GAME_FIRE).onGroup("out_card",this.gameOutCardCb,this),r.default.use(o.default.GAME_FIRE).onGroup("operate_notify",this.gameOperateNotifyCb,this),r.default.use(o.default.GAME_FIRE).onGroup("operate_result",this.gameOperateResultCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_profile",this.gameHeroProfileCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_scene",this.gameHeroSceneCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_enter",this.gameHeroEnterCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_leave",this.gameHeroLeaveCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_offline",this.gameHeroOfflineCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_online",this.gameHeroOnlineCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_sitDown",this.gameHeroSitDownCb,this),r.default.use(o.default.GAME_FIRE).onGroup("hero_standUp",this.gameHeroStandUpCb,this),r.default.use(o.default.GAME_FIRE).onGroup("end",this.endCb,this),r.default.use(o.default.GAME_FIRE).onGroup("dismiss_vote",this.dismissVoteCb,this)}return e.prototype.destroy=function(){r.default.use(o.default.GAME_FIRE).offGroup(this),d.default.destroyInst()},e.prototype.heroEnter=function(e,t,n){this._heroMap[e]=new _(t,!0,!1,n),e==l.default.getInst().getMe().userId&&(this._meChair=t),this.getHeroProfile(e),this._gameLayer.renderHeroSummary(t,n)},e.prototype.getHeroProfile=function(e){null==l.default.getInst().getHero(e)?i.default.use(o.default.GAME_WS_NAME).send(new p.default(e)):this.renderHeroProfile(l.default.getInst().getHero(e))},e.prototype.renderLeave=function(e){this._clientReady&&this._gameLayer.renderLeave(this.switchView(e.chair))},e.prototype.renderHeroProfile=function(e){this._clientReady&&this._gameLayer.renderHeroProfile(this.switchView(this._heroMap[e.userId].chair),this._heroMap[e.userId].chair,e)},e.prototype.renderOnline=function(e){this._clientReady&&this._gameLayer.renderOnline(this.switchView(e.chair),e.online)},e.prototype.renderSitDown=function(e){this._clientReady&&this._gameLayer.renderSitDown(this.switchView(e.chair),e.sitDown,this._gameStart)},e.prototype.getWeaveItems=function(e){return void 0==this._weavesMap[e]&&(this._weavesMap[e]=new Array),this._weavesMap[e]},e.prototype.getDiscardCards=function(e){return void 0==this._discardCardsMap[e]&&(this._discardCardsMap[e]=new Array),this._discardCardsMap[e]},e.prototype.switchView=function(e){return-1==e?-1:(e+this._chairCount-this._meChair)%this._chairCount},e.prototype.switchChair=function(e){return(e+this._meChair)%this._chairCount},e.prototype.getUserId=function(e){var t=this,n="";return s.each(s.keys(this._heroMap),function(r){t._heroMap[r].chair==e&&(n=r)}),n},e.prototype.getHeroMate=function(e){return this._heroMap[this.getUserId(e)]},e}();n.default=g,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../hero/Hero":"Hero","../../hero/HeroManager":"HeroManager","../../ws/AiJKit":"AiJKit","../AbstractRoomConfig":"AbstractRoomConfig","../event/ClientReadyEvent":"ClientReadyEvent","../event/HeroProfileEvent":"HeroProfileEvent","../response/HeroSceneResponse":"HeroSceneResponse","./struct/MahjongWeaveItem":"MahjongWeaveItem","./struct/MahjongWeaveType":"MahjongWeaveType",lodash:6}],MahjongGameLayer:[function(e,t,n){"use strict";cc._RF.push(t,"227feaS5QlMcJvZTEwOL1vd","MahjongGameLayer");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var i=cc._decorator.ccclass,a=e("../../ws/AiJKit"),s=e("../../AppConfig"),u=e("lodash"),c=e("./event/MahjongOutCardEvent"),f=e("./MahjongGameEngine"),l=e("./event/MahjongOperateEvent"),p=e("./struct/MahjongWeaveType"),h=e("md5"),d=e("../event/SitDownTableEvent"),_=e("../../AiJCCComponent"),g=e("../../UIManger"),v=e("../../plazz/PlazaLayer"),y=e("../event/DismissVoteTableEvent"),m=e("../event/LeaveTableEvent"),w=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._headViewMap={},t._gameOverViewMap={},t._endViewMap={},t._countDown=30,t}return r(t,e),t.prototype.onLoad=function(){var e=this;this.loadPackage("mahjong",function(){fgui.UIPackage.addPackage("mahjong"),e._view=fgui.UIPackage.createObject("mahjong","MahjongGameLayer").asCom,fgui.GRoot.inst.addChild(e._view),e.initView()})},t.prototype.onInitAiJCom=function(e){this._engine=new f.default(this,e),this._engine.clientReady()},t.prototype.initFire=function(){},t.prototype.onDestroy=function(){this._engine.destroy(),this._view.dispose()},t.prototype.initView=function(){this._southView=this._view.getChild("SouthComponent").asCom,this._southView.removeChildren(),this._eastView=this._view.getChild("EastComponent").asCom,this._eastView.removeChildren(),this._westView=this._view.getChild("WestComponent").asCom,this._westView.removeChildren(),this._northView=this._view.getChild("NorthComponent").asCom,this._northView.removeChildren(),this._southDiscardView=this._view.getChild("SouthDiscardComponent").asCom,this._southDiscardView.removeChildren(),this._eastDiscardView=this._view.getChild("EastDiscardComponent").asCom,this._eastDiscardView.removeChildren(),this._westDiscardView=this._view.getChild("WestDiscardComponent").asCom,this._westDiscardView.removeChildren(),this._northDiscardView=this._view.getChild("NorthDiscardComponent").asCom,this._northDiscardView.removeChildren(),this._operateNotifyView=this._view.getChild("OperateNotifyComponent").asCom,this._countDownText=this._view.getChild("CountDownText").asTextField,this._leftCardCountText=this._view.getChild("LeftCardCountText").asTextField,this._leftNumberText=this._view.getChild("LeftNumberText").asTextField,this._headViewMap[0]=this._view.getChild("SouthHeadComponent").asCom,this._headViewMap[1]=this._view.getChild("EastHeadComponent").asCom,this._headViewMap[2]=this._view.getChild("NorthHeadComponent").asCom,this._headViewMap[3]=this._view.getChild("WestHeadComponent").asCom;var e=this._view.getChild("GameEndGroup").asGroup;this._gameOverViewMap[0]=this._view.getChildInGroup("SouthGameOverItemComponent",e).asCom,this._gameOverViewMap[1]=this._view.getChildInGroup("EastGameOverItemComponent",e).asCom,this._gameOverViewMap[2]=this._view.getChildInGroup("NorthGameOverItemComponent",e).asCom,this._gameOverViewMap[3]=this._view.getChildInGroup("WestGameOverItemComponent",e).asCom;var t=this._view.getChild("EndGroup").asGroup;this._endViewMap[0]=this._view.getChildInGroup("SouthEndItemComponent",t).asCom,this._endViewMap[1]=this._view.getChildInGroup("EastEndItemComponent",t).asCom,this._endViewMap[2]=this._view.getChildInGroup("NorthEndItemComponent",t).asCom,this._endViewMap[3]=this._view.getChildInGroup("WestEndItemComponent",t).asCom;var n=this._view.getChild("VoteGroup").asGroup;this._voteItemList=this._view.getChildInGroup("VoteItemList",n).asList,this._voteItemList.removeChildren(),this._view.getChildInGroup("NextGameButton",e).asButton.onClick(function(){a.default.use(s.default.GAME_WS_NAME).send(new d.default)},this),this._view.getChild("SitDownButton").asButton.onClick(function(){a.default.use(s.default.GAME_WS_NAME).send(new d.default)},this),this._view.getChild("DismissVoteButton").asButton.onClick(function(){a.default.use(s.default.GAME_WS_NAME).send(new y.default(!0))},this),this._view.getChild("LeaveButton").asButton.onClick(function(){a.default.use(s.default.GAME_WS_NAME).send(new m.default)},this),this._view.getChildInGroup("BackButton",t).asButton.onClick(function(){g.default.getInst().switchLayer(v.default)},this),this._view.getChildInGroup("VoteAgreeButton",n).asButton.onClick(function(){a.default.use(s.default.GAME_WS_NAME).send(new y.default(!0))},this),this._view.getChildInGroup("VoteRefuseButton",n).asButton.onClick(function(){a.default.use(s.default.GAME_WS_NAME).send(new y.default(!1))},this)},t.prototype.renderGameStart=function(){this._view.getChild("SitDownButton").asButton.visible=!1,this._view.getController("c1").setSelectedPage("playing")},t.prototype.renderLeftCardCount=function(e){this._leftCardCountText.text=u.padStart(e.toString(10),2,"0")},t.prototype.renderLeftNumber=function(e){this._leftNumberText.text=u.padStart(e.toString(10),2,"0")},t.prototype.renderDispatchCard=function(){this._countDown=30},t.prototype.renderPilotLamp=function(e){this._view.getChild("pilotLamp0").asImage.visible=!1,this._view.getChild("pilotLamp1").asImage.visible=!1,this._view.getChild("pilotLamp2").asImage.visible=!1,this._view.getChild("pilotLamp3").asImage.visible=!1;var t=this._view.getChild("pilotLamp"+e);null!=t&&(t.asImage.visible=!0)},t.prototype.renderWestCard=function(e,t,n){var r=this;void 0===t&&(t=-1),void 0===n&&(n=null),this._westView.removeChildren();var o=0;u.each(e,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==p.MahjongWeaveType.P?"WestPengComponent":"WestGangComponent").asCom;switch(i.setPosition(0,o),o+=i.height,e.weaveType){case p.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL;break;case p.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL}r._westView.addChild(i)});for(var i=13-3*e.length,a=0;a<i;a++){var s=fgui.UIPackage.createObject("mahjong","w_hand").asImage;null!=n&&(s=fgui.UIPackage.createObject("mahjong","w_mingmah_"+u.padStart(n[a].toString(16),2,"0")).asImage).setScale(.75,.75),s.setPosition(0,o),o+=60,s.sortingOrder=a,this._westView.addChild(s)}if(-1!=t){s=0==t?fgui.UIPackage.createObject("mahjong","w_hand").asImage:fgui.UIPackage.createObject("mahjong","w_mingmah_"+u.padStart(t.toString(16),2,"0")).asImage;0!=t&&s.setScale(.75,.75),s.setPosition(0,this._westView.height-s.height),this._westView.addChild(s)}},t.prototype.renderNorthCard=function(e,t,n){var r=this;void 0===t&&(t=-1),void 0===n&&(n=null),this._northView.removeChildren();var o=this._northView.width;u.each(e,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==p.MahjongWeaveType.P?"NorthPengComponent":"NorthGangComponent").asCom;switch(o-=i.width,i.setPosition(o,r._northView.height-i.height),e.weaveType){case p.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL;break;case p.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL}r._northView.addChild(i)});for(var i=13-3*e.length,a=0;a<i;a++){var s=null==n?fgui.UIPackage.createObject("mahjong","n_hand").asImage:fgui.UIPackage.createObject("mahjong","s_mingmah_"+u.padStart(n[a].toString(16),2,"0")).asImage;o-=s.width,s.setPosition(o,this._northView.height-s.height),this._northView.addChild(s)}if(-1!=t){s=0==t?fgui.UIPackage.createObject("mahjong","n_hand").asImage:fgui.UIPackage.createObject("mahjong","s_mingmah_"+u.padStart(t.toString(16),2,"0")).asImage;o-=s.width+45,s.setPosition(o,this._northView.height-s.height),this._northView.addChild(s)}},t.prototype.renderEastCard=function(e,t,n){var r=this;void 0===t&&(t=-1),void 0===n&&(n=null),this._eastView.removeChildren();var o=this._eastView.height;u.each(e,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==p.MahjongWeaveType.P?"EastPengComponent":"EastGangComponent").asCom;switch(o-=i.height,i.setPosition(r._eastView.width-i.width,o),e.weaveType){case p.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL;break;case p.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL}r._eastView.addChild(i)});for(var i=13-3*e.length,a=0;a<i;a++){var s=fgui.UIPackage.createObject("mahjong","e_hand").asImage;null!=n&&(s=fgui.UIPackage.createObject("mahjong","e_mingmah_"+u.padStart(n[a].toString(16),2,"0")).asImage).setScale(.75,.75),o-=0==a?s.height:60,s.setPosition(this._eastView.width-s.width+.25*s.width,o),s.sortingOrder=i-a,this._eastView.addChild(s)}if(-1!=t){s=0==t?fgui.UIPackage.createObject("mahjong","e_hand").asImage:fgui.UIPackage.createObject("mahjong","e_mingmah_"+u.padStart(t.toString(16),2,"0")).asImage;0!=t&&s.setScale(.75,.75),s.setPosition(this._eastView.width-s.width+.25*s.width,0),this._eastView.addChild(s)}},t.prototype.renderSouthCard=function(e,t,n){var r=this;void 0===n&&(n=-1),this._southView.removeChildren(),this._operateNotifyView.removeChildren();var o=0;u.each(t,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==p.MahjongWeaveType.P?"SouthPengComponent":"SouthGangComponent").asCom;switch(i.setPosition(o,r._southView.height-i.height),e.weaveType){case p.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL;break;case p.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL}o+=i.width,r._southView.addChild(i)}),u.each(e,function(t,i){var f=u.padStart(t.toString(16),2,"0"),l=fgui.UIPackage.createObject("mahjong","SouthCardComponent").asCom;l.setPosition(o+i*l.width+(i+1==e.length&&-1!=n?30:0),r._southView.height-l.height),l.getChild("icon").asLoader.url=fgui.UIPackage.getItemURL("mahjong","s_handmah_"+f),l.draggable=!0,l.data=new C(l.x,l.y,t),l.on(fgui.Event.DRAG_END,function(e){var t=fgui.GObject.cast(e.currentTarget),n=t.data;t.y<=-100&&(a.default.use(s.default.GAME_WS_NAME).send(new c.default(n.value)),console.log("\u6ed1\u52a8\u51fa\u724c:"+n.value)),t.setPosition(n.x,n.y)},r),l.on(fgui.Event.CLICK,function(e){var t=fgui.GObject.cast(e.currentTarget),n=t.data;n.y==t.y?t.setPosition(n.x,n.y-50):t.globalToLocal(e.pos.x,e.pos.y).y<=60?t.setPosition(n.x,n.y):(a.default.use(s.default.GAME_WS_NAME).send(new c.default(n.value)),console.log("\u70b9\u51fb\u51fa\u724c:"+n.value))},r),r._southView.addChild(l)})},t.prototype.renderOperateNotify=function(e,t,n,r,o,i){void 0===o&&(o=!0),void 0===i&&(i=[]),this._operateNotifyView.removeChildren();var u,c=0,f=this._operateNotifyView.height;o&&((u=fgui.UIPackage.createObject("mahjong","GuoButton").asCom).setPosition(c,f-u.height),c+=u.width+20,u.data=e,u.on(fgui.Event.CLICK,function(e){var t=fgui.GObject.cast(e.currentTarget);a.default.use(s.default.GAME_WS_NAME).send(new l.default(0,t.data))},this),this._operateNotifyView.addChild(u));r&&((u=fgui.UIPackage.createObject("mahjong","PengButton").asCom).setPosition(c,f-u.height),c+=u.width+20,u.data=e,u.on(fgui.Event.CLICK,function(e){var t=fgui.GObject.cast(e.currentTarget);a.default.use(s.default.GAME_WS_NAME).send(new l.default(1,t.data))},this),this._operateNotifyView.addChild(u));n&&((u=fgui.UIPackage.createObject("mahjong","GangButton").asCom).setPosition(c,f-u.height),c+=u.width+20,u.data=e,u.on(fgui.Event.CLICK,function(e){var t=fgui.GObject.cast(e.currentTarget);a.default.use(s.default.GAME_WS_NAME).send(new l.default(2,t.data))},this),this._operateNotifyView.addChild(u));t&&((u=fgui.UIPackage.createObject("mahjong","HuButton").asCom).setPosition(c,f-u.height),u.data=e,u.on(fgui.Event.CLICK,function(e){var t=fgui.GObject.cast(e.currentTarget);a.default.use(s.default.GAME_WS_NAME).send(new l.default(4,t.data))},this),this._operateNotifyView.addChild(u))},t.prototype.renderSouthDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._southDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/11).toString()),a=o%11,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","s_mingmah_"+s).asImage;if(c.setPosition(a*c.width,n._southDiscardView.height-(c.height+i*(c.height-26))),c.sortingOrder=11-i,n._southDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(10,-30)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderNorthDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._northDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/11).toString()),a=o%11,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","s_mingmah_"+s).asImage;if(c.setPosition(n._northDiscardView.width-(a+1)*c.width,i*(c.height-26)),c.sortingOrder=i,n._northDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(10,-30)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderEastDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._eastDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/10).toString()),a=o%10,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","e_mingmah_"+s).asImage;if(c.setPosition(n._eastDiscardView.width-(i+1)*c.width,n._eastDiscardView.height-(c.height+a*(c.height-40))),c.sortingOrder=10-a,n._eastDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(0,-20)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderWestDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._westDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/10).toString()),a=o%10,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","w_mingmah_"+s).asImage;if(c.setPosition(i*c.width,a*(c.height-40)),c.sortingOrder=a,n._westDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(40,-20)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderHeroSummary=function(e,t){var n=this._voteItemList.getChild(e.toString());null!=n&&this._voteItemList.removeChild(n);var r=fgui.UIPackage.createObject("mahjong","VoteItemComponent").asCom;r.getChild("NickNameText").asTextField.text=t,r.getChild("VoteResultText").asTextField.text="\u7b49\u5f85",this._voteItemList.addChildAt(r).name=e.toString()},t.prototype.renderHeroProfile=function(e,t,n){if(this._headViewMap[e].getChild("nickName").asTextField.text=n.nickName,this._gameOverViewMap[e].getChild("GameOverHeadItemComponent").asCom.getChild("NickNameText").asTextField.text=n.nickName,this._endViewMap[e].getChild("NickNameText").asTextField.text=n.nickName,this._endViewMap[e].getChild("UserIdText").asTextField.text=n.showId,this._gameOverViewMap[e].getChild("GameOverHeadItemComponent").asCom.getChild("IdText").asTextField.text=n.showId,null!=n.avatar){var r="?name="+h(n.avatar)+".png";this._headViewMap[e].getChild("avatar").asLoader.url=n.avatar+r,this._gameOverViewMap[e].getChild("GameOverHeadItemComponent").asCom.getChild("Avatar").asLoader.url=n.avatar+r,this._endViewMap[e].getChild("AvatarLoader").asLoader.url=n.avatar+r}},t.prototype.renderOnline=function(e,t){this._headViewMap[e].getChild("OfflineImage").asImage.visible=!t},t.prototype.renderSitDown=function(e,t,n){this._headViewMap[e].getChild("SitDownImage").asImage.visible=t&&!n},t.prototype.renderLeave=function(e){this._headViewMap[e].getChild("OfflineImage").asImage.visible=!1,this._headViewMap[e].getChild("SitDownImage").asImage.visible=!1,this._headViewMap[e].getChild("TalkImage").asImage.visible=!1,this._headViewMap[e].getChild("avatar").asLoader.url="",this._headViewMap[e].getChild("nickName").asTextField.text="",this._headViewMap[e].getChild("scoreText").asTextField.text="",0==e&&g.default.getInst().switchLayer(v.default)},t.prototype.renderScore=function(e,t){this._headViewMap[e].getChild("scoreText").asTextField.text=t>=0?"+"+t.toString():t.toString()},t.prototype.renderGameEndCards=function(e,t,n,r,o,i){this._view.getChild("SitDownButton").asButton.visible=!0,this._view.getController("c1").setSelectedPage("gameEnd");var a=this._gameOverViewMap[e].getChild("GameOverCardItemComponent").asCom;a.removeChildren();var s=0;if(u.each(t,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),r=fgui.UIPackage.createObject("mahjong",e.weaveType==p.MahjongWeaveType.P?"SouthPengComponent":"SouthGangComponent").asCom;switch(r.setPosition(s,a.height-r.height),e.weaveType){case p.MahjongWeaveType.P:r.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,r.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,r.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL;break;case p.MahjongWeaveType.G:e.open&&(r.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,r.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,r.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL),r.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL}s+=r.width,a.addChild(r)}),u.each(n,function(e,t){var n=u.padStart(e.toString(16),2,"0"),r=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage;r.setPosition(s,a.height-r.height),s+=r.width,a.addChild(r)}),r){var c=u.padStart(i.toString(16),2,"0"),f=fgui.UIPackage.createObject("mahjong","s_mingmah_"+c).asImage;f.setPosition(s+30,a.height-f.height),a.addChild(f)}},t.prototype.renderGameEndFlag=function(e,t,n,r,o,i){this._headViewMap[e].getChild("scoreText").asTextField.text=n>=0?"+"+n.toString():n.toString(),this._gameOverViewMap[e].getChild("winner").asImage.visible=o,this._gameOverViewMap[e].getChild("InfoText").asTextField.text=t,this._gameOverViewMap[e].getChild("ScoreText").asTextField.text=r>=0?"+"+r.toString():r.toString(),this._gameOverViewMap[e].getChild("GameOverHeadItemComponent").asCom.getChild("Banker").asImage.visible=i,null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent()},t.prototype.renderOutCardBadgeAnimate=function(e,t){this._mahjongOutCardBadgeAnimate=fgui.UIPackage.createObject("mahjong","MahjongOutCardBadgeAnimate").asMovieClip,this._mahjongOutCardBadgeAnimate.setPosition(e,t),this._view.addChild(this._mahjongOutCardBadgeAnimate)},t.prototype.renderEndInfo=function(e,t,n){var r=this._view.getChild("EndGroup").asGroup,o=this._view.getChildInGroup("TableNoText",r).asTextField.data,i=this._view.getChildInGroup("TimeInfoText",r).asTextField.data;this._view.getChildInGroup("TableNoText",r).asTextField.text=u.template(o)({tableNo:e}),this._view.getChildInGroup("TimeInfoText",r).asTextField.text=u.template(i)({startedTime:t,endedTime:n}),this._view.getController("c1").setSelectedPage("end")},t.prototype.renderEnd=function(e,t,n,r,o,i,a,s){this._endViewMap[e].getChild("OwnerImage").asImage.visible=a,this._endViewMap[e].getChild("AgentImage").asImage.visible=""!=s,this._endViewMap[e].getChild("WinnerImage").asImage.visible=t>0,this._endViewMap[e].getChild("ScoreText").asTextField.text=t>=0?"+"+t.toString():t.toString(),this._endViewMap[e].getChild("0Text").asTextField.text=n[0].toString(),this._endViewMap[e].getChild("1Text").asTextField.text=n[1].toString(),this._endViewMap[e].getChild("2Text").asTextField.text=n[2].toString(),this._endViewMap[e].getChild("3Text").asTextField.text=n[3].toString(),this._endViewMap[e].getChild("4Text").asTextField.text=n[4].toString(),this._endViewMap[e].getChild("5Text").asTextField.text=n[5].toString()},t.prototype.renderDismissVote=function(e,t,n,r,o,i){var a=this,s=this._view.getChild("VoteGroup").asGroup;if(1==e)this._view.getChildInGroup("VoteTimeText",s).asTextField.text=r,this._view.getChildInGroup("VoteCountDownText",s).asTextField.text=o.toString(),this._view.getController("c1").setSelectedPage("vote"),null!=t&&(-1!=u.indexOf(t,i)&&(this._view.getChildInGroup("VoteAgreeButton",s).asButton.visible=!1,this._view.getChildInGroup("VoteRefuseButton",s).asButton.visible=!1),u.each(t,function(e){a._voteItemList.getChild(e.toString()).asCom.getChild("VoteResultText").asTextField.text="\u540c\u610f"})),null!=n&&(-1!=u.indexOf(n,i)&&(this._view.getChildInGroup("VoteAgreeButton",s).asButton.visible=!1,this._view.getChildInGroup("VoteRefuseButton",s).asButton.visible=!1),u.each(n,function(e){a._voteItemList.getChild(e.toString()).asCom.getChild("VoteResultText").asTextField.text="\u62d2\u7edd"}));else{this._view.getChildInGroup("VoteAgreeButton",s).asButton.visible=!0,this._view.getChildInGroup("VoteRefuseButton",s).asButton.visible=!0,this._view.getController("c1").setSelectedPage("playing");for(var c=this._voteItemList.numChildren,f=0;f<c;f++)this._voteItemList.getChild(f.toString()).asCom.getChild("VoteResultText").asTextField.text="\u7b49\u5f85"}},t=o([i],t)}(_.default);n.default=w;var C=function(){return function(e,t,n){this.x=e,this.y=t,this.value=n}}();cc._RF.pop()},{"../../AiJCCComponent":"AiJCCComponent","../../AppConfig":"AppConfig","../../UIManger":"UIManger","../../plazz/PlazaLayer":"PlazaLayer","../../ws/AiJKit":"AiJKit","../event/DismissVoteTableEvent":"DismissVoteTableEvent","../event/LeaveTableEvent":"LeaveTableEvent","../event/SitDownTableEvent":"SitDownTableEvent","./MahjongGameEngine":"MahjongGameEngine","./event/MahjongOperateEvent":"MahjongOperateEvent","./event/MahjongOutCardEvent":"MahjongOutCardEvent","./struct/MahjongWeaveType":"MahjongWeaveType",lodash:6,md5:7}],MahjongGameRecord:[function(e,t,n){"use strict";cc._RF.push(t,"33ecet6YvBGKqP2pBtxFVBb","MahjongGameRecord"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){return function(){}}();n.default=r,cc._RF.pop()},{}],MahjongGameStartEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"7a4d8ZjAxtIybJ3rggPZHz0","MahjongGameStartEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongGameStartRecord:[function(e,t,n){"use strict";cc._RF.push(t,"63831n6n1dM95QAP7yqarLO","MahjongGameStartRecord"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){return function(){}}();n.default=r,cc._RF.pop()},{}],MahjongGameStartResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"f5d97aB6DhMs7/Oe5CtAz9i","MahjongGameStartResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("game_start",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongGameStatusResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"849baGkDD9MVLlVOhkPPl34","MahjongGameStatusResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("game_status",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongGameStatusResponse:[function(e,t,n){"use strict";cc._RF.push(t,"998b70YMZxBN4za0V1hh/0X","MahjongGameStatusResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongOperateEvent:[function(e,t,n){"use strict";cc._RF.push(t,"aadf1LwziZHLKnPMXQ9/poU","MahjongOperateEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t,n){var r=e.call(this)||this;return r.action=t,r.card=n,r.mainType=8,r.subType=1,r}return r(t,e),t}(e("../../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongOperateNotifyEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"8a5cb/ROHRPF4kYxMqRmph5","MahjongOperateNotifyEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("operate_notify",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongOperateNotifyEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"13c957E0UlJv5IECe5iMoWn","MahjongOperateNotifyEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongOperateResultEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"d2880CREslDYb9HnrBPZpOn","MahjongOperateResultEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("operate_result",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongOperateResultEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"2d3942MIWZBe4jmwLTT5g6X","MahjongOperateResultEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongOutCardEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"d35babWKElHz4e57Jq7PYpK","MahjongOutCardEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongOutCardEvent:[function(e,t,n){"use strict";cc._RF.push(t,"6c08b+kCS9PBagcebEAnMxq","MahjongOutCardEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t){var n=e.call(this)||this;return n.mainType=8,n.subType=0,n.card=t,n}return r(t,e),t}(e("../../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongOutCardResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"ac3faVWXxFDrpRKqCJlIaRD","MahjongOutCardResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("out_card",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongPlayerRecord:[function(e,t,n){"use strict";cc._RF.push(t,"a5475BID+hBT6ZZOQMI0wyV","MahjongPlayerRecord"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){return function(){}}();n.default=r,cc._RF.pop()},{}],MahjongPlayingGameSceneResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"07599nYPp1O3orkkeGXsWNX","MahjongPlayingGameSceneResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../ws/AiJ"),i=e("../../../fire/FireKit"),a=e("../../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).fire("playing_scene",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../../AppConfig":"AppConfig","../../../fire/FireKit":"FireKit","../../../ws/AiJ":"AiJ"}],MahjongPlayingGameSceneResponse:[function(e,t,n){"use strict";cc._RF.push(t,"659dfY7o7lMQ4eeFCMLP0l+","MahjongPlayingGameSceneResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongPrepareGameSceneResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"02c908c/k5JK7ZD5zO10WdA","MahjongPrepareGameSceneResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){},t}(e("../../../ws/AiJ").AiJ.ResponseHandler);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongPrepareGameSceneResponse:[function(e,t,n){"use strict";cc._RF.push(t,"fda8bMSlNhLR54nhF1bG9D9","MahjongPrepareGameSceneResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../../ws/AiJ":"AiJ"}],MahjongRecord:[function(e,t,n){"use strict";cc._RF.push(t,"612eeh5yplEbq/InWPMQ+YY","MahjongRecord"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){return function(){}}();n.default=r,cc._RF.pop()},{}],MahjongRoomConfig:[function(e,t,n){"use strict";cc._RF.push(t,"9c0e5cRlKRPf5H+vZYuLmty","MahjongRoomConfig");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJKit"),i=e("../../fire/FireKit"),a=e("../handler/RoomLoginResponseHandler"),s=e("../handler/LoginNotifyResponseHandler"),u=e("../event/RoomMobileLoginEvent"),c=e("../../AppConfig"),f=e("../handler/CreateTableEventResponseHandler"),l=e("../event/JoinTableEvent"),p=e("../handler/JoinTableEventResponseHandler"),h=e("../../UIManger"),d=e("./MahjongGameLayer"),_=e("../RoomWsListener"),g=e("../handler/HeroOnlineEventResponseHandler"),v=e("../handler/HeroEnterEventResponseHandler"),y=e("../handler/HeroLeaveEventResponseHandler"),m=e("../handler/HeroOfflineEventResponseHandler"),w=e("../handler/HeroSitDownEventResponseHandler"),C=e("../handler/HeroStandUpEventResponseHandler"),R=e("../handler/HeroSceneResponseHandler"),A=e("../handler/ChatEventResponseHandler"),b=e("../handler/RoomCommonResponseHandler"),O=e("./handler/MahjongGameStartResponseHandler"),j=e("./handler/MahjongGameStatusResponseHandler"),P=e("./handler/MahjongPlayingGameSceneResponseHandler"),E=e("./handler/MahjongDispathCardResponseHandler"),I=e("./handler/MahjongOutCardResponseHandler"),M=e("./handler/MahjongOperateNotifyEventResponseHandler"),F=e("./handler/MahjongOperateResultEventResponseHandler"),L=e("./handler/MahjongErrorEventResponseHandler"),J=e("../handler/HeroProfileEventResponseHandler"),T=e("./handler/MahjongGameEndEventResponseHandler"),x=e("./handler/MahjongPrepareGameSceneResponseHandler"),S=e("./handler/MahjongEndEventResponseHandler"),H=e("../handler/DismissVoteEventResponseHandler"),U=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r._config.addRouter(0,0,new b.default),r._config.addRouter(1,1,new a.default),r._config.addRouter(1,2,new s.default),r._config.addRouter(2,1,new f.default),r._config.addRouter(2,2,new p.default),r._config.addRouter(2,3,new v.default),r._config.addRouter(2,4,new y.default),r._config.addRouter(2,5,new g.default),r._config.addRouter(2,6,new m.default),r._config.addRouter(2,7,new w.default),r._config.addRouter(2,8,new C.default),r._config.addRouter(2,9,new R.default),r._config.addRouter(2,10,new A.default),r._config.addRouter(2,11,new J.default),r._config.addRouter(2,12,new H.default),r._config.addRouter(8,-1,new L.default),r._config.addRouter(8,0,new O.default),r._config.addRouter(8,1,new E.default),r._config.addRouter(8,2,new I.default),r._config.addRouter(8,3,new M.default),r._config.addRouter(8,4,new F.default),r._config.addRouter(8,5,new j.default),r._config.addRouter(8,6,new P.default),r._config.addRouter(8,7,new x.default),r._config.addRouter(8,8,new T.default),r._config.addRouter(8,9,new S.default),r._config.setWsEventListener(new _.default),r}return r(t,e),t.prototype.onCreate=function(){i.default.use(c.default.GAME_FIRE).onGroup("open",t.onOpen,this),i.default.use(c.default.GAME_FIRE).onGroup("create_table_success",t.onCreateTableSuccess,this),i.default.use(c.default.GAME_FIRE).onGroup("join_table_success",t.onJoinTableSuccess,this)},t.prototype.onDestroy=function(){i.default.use(c.default.GAME_FIRE).offGroup(this)},t.onOpen=function(){var e=JSON.parse(cc.sys.localStorage.getItem("user"));o.default.use(c.default.GAME_WS_NAME).send(new u.default(e.username,e.password))},t.onCreateTableSuccess=function(e){o.default.use(c.default.GAME_WS_NAME).send(new l.default(e.tableNo))},t.onJoinTableSuccess=function(e){h.default.getInst().switchLayer(d.default,e)},t}(e("../AbstractRoomConfig").default);n.default=U,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../UIManger":"UIManger","../../fire/FireKit":"FireKit","../../ws/AiJKit":"AiJKit","../AbstractRoomConfig":"AbstractRoomConfig","../RoomWsListener":"RoomWsListener","../event/JoinTableEvent":"JoinTableEvent","../event/RoomMobileLoginEvent":"RoomMobileLoginEvent","../handler/ChatEventResponseHandler":"ChatEventResponseHandler","../handler/CreateTableEventResponseHandler":"CreateTableEventResponseHandler","../handler/DismissVoteEventResponseHandler":"DismissVoteEventResponseHandler","../handler/HeroEnterEventResponseHandler":"HeroEnterEventResponseHandler","../handler/HeroLeaveEventResponseHandler":"HeroLeaveEventResponseHandler","../handler/HeroOfflineEventResponseHandler":"HeroOfflineEventResponseHandler","../handler/HeroOnlineEventResponseHandler":"HeroOnlineEventResponseHandler","../handler/HeroProfileEventResponseHandler":"HeroProfileEventResponseHandler","../handler/HeroSceneResponseHandler":"HeroSceneResponseHandler","../handler/HeroSitDownEventResponseHandler":"HeroSitDownEventResponseHandler","../handler/HeroStandUpEventResponseHandler":"HeroStandUpEventResponseHandler","../handler/JoinTableEventResponseHandler":"JoinTableEventResponseHandler","../handler/LoginNotifyResponseHandler":"LoginNotifyResponseHandler","../handler/RoomCommonResponseHandler":"RoomCommonResponseHandler","../handler/RoomLoginResponseHandler":"RoomLoginResponseHandler","./MahjongGameLayer":"MahjongGameLayer","./handler/MahjongDispathCardResponseHandler":"MahjongDispathCardResponseHandler","./handler/MahjongEndEventResponseHandler":"MahjongEndEventResponseHandler","./handler/MahjongErrorEventResponseHandler":"MahjongErrorEventResponseHandler","./handler/MahjongGameEndEventResponseHandler":"MahjongGameEndEventResponseHandler","./handler/MahjongGameStartResponseHandler":"MahjongGameStartResponseHandler","./handler/MahjongGameStatusResponseHandler":"MahjongGameStatusResponseHandler","./handler/MahjongOperateNotifyEventResponseHandler":"MahjongOperateNotifyEventResponseHandler","./handler/MahjongOperateResultEventResponseHandler":"MahjongOperateResultEventResponseHandler","./handler/MahjongOutCardResponseHandler":"MahjongOutCardResponseHandler","./handler/MahjongPlayingGameSceneResponseHandler":"MahjongPlayingGameSceneResponseHandler","./handler/MahjongPrepareGameSceneResponseHandler":"MahjongPrepareGameSceneResponseHandler"}],MahjongVideoLayer:[function(e,t,n){"use strict";cc._RF.push(t,"e5def0aWTtHE4umGOs8M53V","MahjongVideoLayer");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var i=e("md5"),a=cc._decorator.ccclass,s=e("../../AiJCCComponent"),u=e("lodash"),c=e("../../hero/HeroManager"),f=e("./struct/MahjongWeaveItem"),l=e("./struct/MahjongWeaveType"),p=e("./record/MahjongAction"),h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._headViewMap={},t._chairCards={},t._weavesMap={},t._discardCardsMap={},t._currentIndex=0,t._currCard=-1,t._timeout=1e3,t._timeoutFnId=-1,t}return r(t,e),t.prototype.onLoad=function(){var e=this;this.loadPackage("mahjong",function(){fgui.UIPackage.addPackage("mahjong"),e._view=fgui.UIPackage.createObject("mahjong","MahjongVideoLayer").asCom,fgui.GRoot.inst.addChild(e._view),e.initView()})},t.prototype.onInitAiJCom=function(e){var t=JSON.parse(e.detail);this._mahjongGameRecord=t.mahjongGameRecords[parseInt(e.index)],this._mahjongPlayerRecords=t.mahjongPlayerRecords,this._chairCount=this._mahjongPlayerRecords.length,this._meChair=this.getMeChair(),this.renderHead(),this.renderGameStart(),this.playRecord()},t.prototype.playRecord=function(){var e=this,t=this._mahjongGameRecord.mahjongGameActionRecords.length;this._countDownText.text=u.padStart((t-this._currentIndex-1).toString(),2,"0");var n=this._mahjongGameRecord.mahjongGameActionRecords[this._currentIndex];console.log(JSON.stringify(n));var r=this._timeout;switch(n.mahjongAction){case p.MahjongAction.DISPATCH:this.dispatchCard(n);break;case p.MahjongAction.NOTIFY:break;case p.MahjongAction.OUT:this.outCard(n);break;case p.MahjongAction.N:break;case p.MahjongAction.P:this.operateCard(1,n);break;case p.MahjongAction.G:this.operateCard(2,n);break;case p.MahjongAction.H:this.operateCard(4,n)}++this._currentIndex<t&&(this._timeoutFnId=window.setTimeout(function(){e.playRecord()},r))},t.prototype.onDestroy=function(){this._view.dispose()},t.prototype.initView=function(){var e=this;this._southView=this._view.getChild("SouthComponent").asCom,this._southView.removeChildren(),this._eastView=this._view.getChild("EastComponent").asCom,this._eastView.removeChildren(),this._westView=this._view.getChild("WestComponent").asCom,this._westView.removeChildren(),this._northView=this._view.getChild("NorthComponent").asCom,this._northView.removeChildren(),this._southDiscardView=this._view.getChild("SouthDiscardComponent").asCom,this._southDiscardView.removeChildren(),this._eastDiscardView=this._view.getChild("EastDiscardComponent").asCom,this._eastDiscardView.removeChildren(),this._westDiscardView=this._view.getChild("WestDiscardComponent").asCom,this._westDiscardView.removeChildren(),this._northDiscardView=this._view.getChild("NorthDiscardComponent").asCom,this._northDiscardView.removeChildren(),this._headViewMap[0]=this._view.getChild("SouthHeadComponent").asCom,this._headViewMap[1]=this._view.getChild("EastHeadComponent").asCom,this._headViewMap[2]=this._view.getChild("NorthHeadComponent").asCom,this._headViewMap[3]=this._view.getChild("WestHeadComponent").asCom,this._countDownText=this._view.getChild("CountDownText").asTextField,this._view.getChild("BackwardButton").asButton.onClick(function(){e._timeout<4e3&&(e._timeout+=100)},this),this._view.getChild("PauseButton").asButton.onClick(function(){-1==e._timeoutFnId?(e._view.getChild("PauseButton").asButton.icon=fgui.UIPackage.getItemURL("mahjong","rec_pause"),e.playRecord()):(window.clearTimeout(e._timeoutFnId),e._view.getChild("PauseButton").asButton.icon=fgui.UIPackage.getItemURL("mahjong","rec_play"),e._timeoutFnId=-1)},this),this._view.getChild("ForwardButton").asButton.onClick(function(){e._timeout>300&&(e._timeout-=100)},this),this._view.getChild("ExitButton").asButton.onClick(function(){-1!=e._timeoutFnId&&window.clearTimeout(e._timeoutFnId),e.destroy()},this)},t.prototype.renderHead=function(){var e=this;u.each(this._mahjongPlayerRecords,function(t,n){if(e._headViewMap[n].getChild("nickName").asTextField.text=t.nickName,null!=t.avatar){var r="?name="+i(t.avatar)+".png";e._headViewMap[n].getChild("avatar").asLoader.url=t.avatar+r}})},t.prototype.renderGameStart=function(){var e=this;u.each(this._mahjongGameRecord.mahjongGameStartRecord,function(t,n){switch(e.switchView(n)){case 0:e.renderSouthCard(t.cards,[]);break;case 1:e.renderEastCard(t.cards,[]);break;case 2:e.renderNorthCard(t.cards,[]);break;case 3:e.renderWestCard(t.cards,[])}e._chairCards[n]=t.cards})},t.prototype.renderSouthCard=function(e,t,n){var r=this;void 0===n&&(n=-1),this._southView.removeChildren();var o=0;u.each(t,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==l.MahjongWeaveType.P?"SouthPengComponent":"SouthGangComponent").asCom;switch(i.setPosition(o,r._southView.height-i.height),e.weaveType){case l.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL;break;case l.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL}o+=i.width,r._southView.addChild(i)}),u.each(e,function(t,i){u.padStart(t.toString(16),2,"0");var a=fgui.UIPackage.createObject("mahjong","s_mingmah_"+u.padStart(e[i].toString(16),2,"0")).asImage;a.setPosition(o+i*a.width+(i+1==e.length&&-1!=n?30:0),r._southView.height-a.height),r._southView.addChild(a)})},t.prototype.renderEastCard=function(e,t,n){var r=this;void 0===e&&(e=null),void 0===n&&(n=-1),this._eastView.removeChildren();var o=this._eastView.height;u.each(t,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==l.MahjongWeaveType.P?"EastPengComponent":"EastGangComponent").asCom;switch(o-=i.height,i.setPosition(r._eastView.width-i.width,o),e.weaveType){case l.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL;break;case l.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","e_mingmah_"+n).asImage.resourceURL}r._eastView.addChild(i)});for(var i=13-3*t.length,a=0;a<i;a++){var s;(s=fgui.UIPackage.createObject("mahjong","e_mingmah_"+u.padStart(e[a].toString(16),2,"0")).asImage).setScale(.75,.75),o-=0==a?s.height:60,s.setPosition(this._eastView.width-s.width+.25*s.width,o),s.sortingOrder=i-a,this._eastView.addChild(s)}-1!=n&&((s=fgui.UIPackage.createObject("mahjong","e_mingmah_"+u.padStart(n.toString(16),2,"0")).asImage).setScale(.75,.75),s.setPosition(this._eastView.width-s.width+.25*s.width,0),this._eastView.addChild(s))},t.prototype.renderNorthCard=function(e,t,n){var r=this;void 0===e&&(e=null),void 0===n&&(n=-1),this._northView.removeChildren();var o=this._northView.width;u.each(t,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==l.MahjongWeaveType.P?"NorthPengComponent":"NorthGangComponent").asCom;switch(o-=i.width,i.setPosition(o,r._northView.height-i.height),e.weaveType){case l.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL;break;case l.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","s_mingmah_"+n).asImage.resourceURL}r._northView.addChild(i)});for(var i=13-3*t.length,a=0;a<i;a++){var s=fgui.UIPackage.createObject("mahjong","s_mingmah_"+u.padStart(e[a].toString(16),2,"0")).asImage;o-=s.width,s.setPosition(o,this._northView.height-s.height),this._northView.addChild(s)}if(-1!=n){s=fgui.UIPackage.createObject("mahjong","s_mingmah_"+u.padStart(n.toString(16),2,"0")).asImage;o-=s.width+45,s.setPosition(o,this._northView.height-s.height),this._northView.addChild(s)}},t.prototype.renderWestCard=function(e,t,n){var r=this;void 0===e&&(e=null),void 0===n&&(n=-1),this._westView.removeChildren();var o=0;u.each(t,function(e,t){var n=u.padStart(e.centerCard.toString(16),2,"0"),i=fgui.UIPackage.createObject("mahjong",e.weaveType==l.MahjongWeaveType.P?"WestPengComponent":"WestGangComponent").asCom;switch(i.setPosition(0,o),o+=i.height,e.weaveType){case l.MahjongWeaveType.P:i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL;break;case l.MahjongWeaveType.G:e.open&&(i.getChild("n0").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n1").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL,i.getChild("n2").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL),i.getChild("n3").asLoader.url=fgui.UIPackage.createObject("mahjong","w_mingmah_"+n).asImage.resourceURL}r._westView.addChild(i)});for(var i=13-3*t.length,a=0;a<i;a++){var s;(s=fgui.UIPackage.createObject("mahjong","w_mingmah_"+u.padStart(e[a].toString(16),2,"0")).asImage).setScale(.75,.75),s.setPosition(0,o),o+=60,s.sortingOrder=a,this._westView.addChild(s)}-1!=n&&((s=fgui.UIPackage.createObject("mahjong","w_mingmah_"+u.padStart(n.toString(16),2,"0")).asImage).setScale(.75,.75),s.setPosition(0,this._westView.height-s.height),this._westView.addChild(s))},t.prototype.getWeaveItems=function(e){return void 0==this._weavesMap[e]&&(this._weavesMap[e]=new Array),this._weavesMap[e]},t.prototype.getDiscardCards=function(e){return void 0==this._discardCardsMap[e]&&(this._discardCardsMap[e]=new Array),this._discardCardsMap[e]},t.prototype.switchView=function(e){return-1==e?-1:(e+this._chairCount-this._meChair)%this._chairCount},t.prototype.switchChair=function(e){return(e+this._meChair)%this._chairCount},t.prototype.getMeChair=function(){var e=this;return this._meChair=0,u.each(this._mahjongPlayerRecords,function(t,n){c.default.getInst().getMe().userId==t.userId&&(e._meChair=n)}),this._meChair},t.prototype.operateCard=function(e,t){var n=this.getWeaveItems(t.chair),r=this.getDiscardCards(t.provider),o=0;switch(e){case 0:break;case 1:u.remove(this._chairCards[t.chair],function(e){return e==t.card&&o++<2}),t.chair!=t.provider&&(r=u.dropRight(r,1),this._discardCardsMap[t.provider]=r),n.push(new f.default(l.MahjongWeaveType.P,t.card,!0,t.provider));break;case 2:var i=u.find(n,{centerCard:t.card,weaveType:l.MahjongWeaveType.P});u.remove(this._chairCards[t.chair],function(e){return e==t.card&&o++<(null==i?3:1)}),t.chair!=t.provider&&(r=u.dropRight(r,1),this._discardCardsMap[t.provider]=r),null==i?n.push(new f.default(l.MahjongWeaveType.G,t.card,t.provider!=t.chair,t.provider)):i.weaveType=l.MahjongWeaveType.G;break;case 4:return}switch(this.switchView(t.chair)){case 0:this.renderSouthCard(u.clone(this._chairCards[t.chair]),n);break;case 1:this.renderEastCard(u.clone(this._chairCards[t.chair]),n);break;case 2:this.renderNorthCard(u.clone(this._chairCards[t.chair]),n);break;case 3:this.renderWestCard(u.clone(this._chairCards[t.chair]),n)}switch(this.switchView(t.provider)){case 0:this.renderSouthDiscardCard(r);break;case 1:this.renderEastDiscardCard(r);break;case 2:this.renderNorthDiscardCard(r);break;case 3:this.renderWestDiscardCard(r)}},t.prototype.dispatchCard=function(e){var t=this.getWeaveItems(e.chair);switch(this._chairCards[e.chair].push(e.card),this._currCard=e.card,this.switchView(e.chair)){case 0:this.renderSouthCard(u.clone(this._chairCards[e.chair]),t,this._currCard);break;case 1:this.renderEastCard(u.clone(this._chairCards[e.chair]),t,this._currCard);break;case 2:this.renderNorthCard(u.clone(this._chairCards[e.chair]),t,this._currCard);break;case 3:this.renderWestCard(u.clone(this._chairCards[e.chair]),t,this._currCard)}},t.prototype.outCard=function(e){var t=this.getWeaveItems(e.chair),n=this.getDiscardCards(e.chair);n.push(e.card);var r=this._chairCards[e.chair].indexOf(e.card);switch(this._chairCards[e.chair].splice(r,1),this._chairCards[e.chair]=u.sortBy(this._chairCards[e.chair]),this.switchView(e.chair)){case 0:this.renderSouthDiscardCard(n,!0),this.renderSouthCard(u.clone(this._chairCards[e.chair]),t);break;case 1:this.renderEastDiscardCard(n,!0),this.renderEastCard(u.clone(this._chairCards[e.chair]),t);break;case 2:this.renderNorthDiscardCard(n,!0),this.renderNorthCard(u.clone(this._chairCards[e.chair]),t);break;case 3:this.renderWestDiscardCard(n,!0),this.renderWestCard(u.clone(this._chairCards[e.chair]),t)}},t.prototype.renderSouthDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._southDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/11).toString()),a=o%11,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","s_mingmah_"+s).asImage;if(c.setPosition(a*c.width,n._southDiscardView.height-(c.height+i*(c.height-26))),c.sortingOrder=11-i,n._southDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(10,-30)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderNorthDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._northDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/11).toString()),a=o%11,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","s_mingmah_"+s).asImage;if(c.setPosition(n._northDiscardView.width-(a+1)*c.width,i*(c.height-26)),c.sortingOrder=i,n._northDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(10,-30)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderEastDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._eastDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/10).toString()),a=o%10,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","e_mingmah_"+s).asImage;if(c.setPosition(n._eastDiscardView.width-(i+1)*c.width,n._eastDiscardView.height-(c.height+a*(c.height-40))),c.sortingOrder=10-a,n._eastDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(0,-20)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderWestDiscardCard=function(e,t){var n=this;void 0===t&&(t=!1),this._westDiscardView.removeChildren(),null!=this._mahjongOutCardBadgeAnimate&&this._mahjongOutCardBadgeAnimate.removeFromParent();u.each(e,function(r,o){var i=parseInt((o/10).toString()),a=o%10,s=u.padStart(r.toString(16),2,"0"),c=fgui.UIPackage.createObject("mahjong","w_mingmah_"+s).asImage;if(c.setPosition(i*c.width,a*(c.height-40)),c.sortingOrder=a,n._westDiscardView.addChild(c),t&&o==e.length-1){var f=c.localToGlobal(cc.Vec2.ZERO.x,cc.Vec2.ZERO.y);f=f.addSelf(new cc.Vec2(40,-20)),n.renderOutCardBadgeAnimate(f.x,f.y)}})},t.prototype.renderOutCardBadgeAnimate=function(e,t){this._mahjongOutCardBadgeAnimate=fgui.UIPackage.createObject("mahjong","MahjongOutCardBadgeAnimate").asMovieClip,this._mahjongOutCardBadgeAnimate.setPosition(e,t),this._view.addChild(this._mahjongOutCardBadgeAnimate)},t=o([a],t)}(s.default);n.default=h,cc._RF.pop()},{"../../AiJCCComponent":"AiJCCComponent","../../hero/HeroManager":"HeroManager","./record/MahjongAction":"MahjongAction","./struct/MahjongWeaveItem":"MahjongWeaveItem","./struct/MahjongWeaveType":"MahjongWeaveType",lodash:6,md5:7}],MahjongWeaveItem:[function(e,t,n){"use strict";cc._RF.push(t,"5372fwYt4hIZoJKE8latdwu","MahjongWeaveItem"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){return function(e,t,n,r){this.weaveType=e,this.centerCard=t,this.open=n,this.provider=r}}();n.default=r,cc._RF.pop()},{}],MahjongWeaveType:[function(e,t,n){"use strict";cc._RF.push(t,"cef95gfvFpLZLotc9bSHbqF","MahjongWeaveType"),Object.defineProperty(n,"__esModule",{value:!0}),function(e){e[e.C=1]="C",e[e.P=2]="P",e[e.G=3]="G"}(n.MahjongWeaveType||(n.MahjongWeaveType={})),cc._RF.pop()},{}],OnFire:[function(e,t,n){"use strict";cc._RF.push(t,"cbc4ejla2RKILNPeg/0yhii","OnFire"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(){this.es={},this.emit=this.fire}return e.prototype.on=function(e,t,n){void 0===n&&(n=!1),this.es[e]||(this.es[e]=[]),this.es[e].push({cb:t,once:n,group:""})},e.prototype.onGroup=function(e,t,n){this.es[e]||(this.es[e]=[]),this.es[e].push({cb:t,once:!1,group:n})},e.prototype.once=function(e,t){this.on(e,t,!0)},e.prototype.fire=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];for(var r=this.es[e]||[],o=r.length,i=0;i<o;i++){var a=r[i],s=a.cb,u=a.once;s.apply(this,t),u&&(r.splice(i,1),i--,o--)}},e.prototype.offGroup=function(e){for(var t in this.es)for(var n=this.es[t]||[],r=n.length,o=0;o<r;o++)n[o].group===e&&(n.splice(o,1),o--,r--)},e.prototype.off=function(e,t){if(void 0===e)this.es={};else if(void 0===t)delete this.es[e];else for(var n=this.es[e]||[],r=n.length,o=0;o<r;o++)n[o].cb===t&&(n.splice(o,1),o--,r--)},e.ver="__VERSION__",e}();n.default=r,cc._RF.pop()},{}],PlazaCommonResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"66dddx69sZIT65+KSMMzxtT","PlazaCommonResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../AlertWindow"),a=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.alert("\u63d0\u793a\u4fe1\u606f",t.message)},t}(o.AiJ.ResponseHandler);n.default=a,cc._RF.pop()},{"../../AlertWindow":"AlertWindow","../../ws/AiJ":"AiJ"}],PlazaConfig:[function(e,t,n){"use strict";cc._RF.push(t,"2a0a8PUb8JF9YDt/lToNXTh","PlazaConfig"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("../ws/AiJ"),o=e("../AppConfig"),i=e("./handler/PlazaCommonResponseHandler"),a=e("./handler/PlazaLoginHandler"),s=e("./handler/RoomEventResponseHandler"),u=e("./PlazaWsListener"),c=e("../ws/AiJKit"),f=e("./event/PlazaMobileLoginEvent"),l=e("../fire/FireKit"),p=e("./handler/RoomRecordEventResponseHandler"),h=e("./handler/BroadcastEventResponseHandler"),d=e("./handler/UserAssetEventResponseHandler"),_=e("./handler/UserAssetTransEventResponseHandler"),g=e("./handler/RechargeRecordEventResponseHandler"),v=e("./handler/UserCertEventResponseHandler"),y=function(){function e(e,t){this.login=function(){var e=cc.sys.localStorage.getItem("user");if(null!=e&&e.length>0){var t=JSON.parse(e);c.default.use(o.default.PLAZA_WS_NAME).send(new f.default(t.username,t.password))}},this.url="ws://"+e+":"+t,this._config=new r.AiJ.Config(this.url,new r.AiJ.Options),this._config.addRouter(0,0,new i.default),this._config.addRouter(1,1,new a.default),this._config.addRouter(2,2,new h.default),this._config.addRouter(3,1,new s.default),this._config.addRouter(3,2,new p.default),this._config.addRouter(4,1,new d.default),this._config.addRouter(4,2,new _.default),this._config.addRouter(4,3,new g.default),this._config.addRouter(4,4,new v.default),this._config.setWsEventListener(new u.default),l.default.use(o.default.LOCAL_FIRE).on("login",this.login),c.default.init(o.default.PLAZA_WS_NAME,this._config),this._aiJPro=c.default.use(o.default.PLAZA_WS_NAME)}return e.init=function(t,n){null!=e.inst&&e.inst.close(),e.inst=new e(t,n)},e.getInst=function(){return e.inst},e.prototype.close=function(){c.default.close(o.default.PLAZA_WS_NAME),l.default.use(o.default.LOCAL_FIRE).off("login",this.login)},e}();n.default=y,cc._RF.pop()},{"../AppConfig":"AppConfig","../fire/FireKit":"FireKit","../ws/AiJ":"AiJ","../ws/AiJKit":"AiJKit","./PlazaWsListener":"PlazaWsListener","./event/PlazaMobileLoginEvent":"PlazaMobileLoginEvent","./handler/BroadcastEventResponseHandler":"BroadcastEventResponseHandler","./handler/PlazaCommonResponseHandler":"PlazaCommonResponseHandler","./handler/PlazaLoginHandler":"PlazaLoginHandler","./handler/RechargeRecordEventResponseHandler":"RechargeRecordEventResponseHandler","./handler/RoomEventResponseHandler":"RoomEventResponseHandler","./handler/RoomRecordEventResponseHandler":"RoomRecordEventResponseHandler","./handler/UserAssetEventResponseHandler":"UserAssetEventResponseHandler","./handler/UserAssetTransEventResponseHandler":"UserAssetTransEventResponseHandler","./handler/UserCertEventResponseHandler":"UserCertEventResponseHandler"}],PlazaLayer:[function(e,t,n){"use strict";cc._RF.push(t,"1a94965c7VLipk/Qy3eVYOh","PlazaLayer");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var i=cc._decorator.ccclass,a=e("../UIManger"),s=e("../AppConfig"),u=e("../plazz/event/RoomEvent"),c=e("../fire/FireKit"),f=e("../GameServiceManager"),l=e("../AlertWindow"),p=e("../room/mahjong/MahjongRoomConfig"),h=e("../plazz/PlazaConfig"),d=e("lodash"),_=e("../ws/AiJKit"),g=e("../room/event/CreateTableEvent"),v=e("../room/event/JoinTableEvent"),y=e("./event/RoomRecordEvent"),m=e("../AiJCCComponent"),w=e("./RoomRecordLayer"),C=e("./event/BroadcastEvent"),R=e("./event/UserAssetEvent"),A=e("../hero/HeroManager"),b=e("../UserInfoWindow"),O=e("./event/UserAssetTransEvent"),j=e("./RechargeRecordLayer"),P=e("./event/RechargeRecordEvent"),E=e("./event/UserCertEvent"),I=e("../SettingWindow"),M=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.tableNo=[],t.roomRecordCb=function(e){a.default.getInst().switchLayer(w.default,e)},t.rechargeRecordCb=function(e){a.default.getInst().switchLayer(j.default,e,!1)},t.roomCb=function(e){f.default.getInst().initGameService(e.roomItems)},t.broadcastCb=function(e){t._view.getChild("MessageText").asTextField.text=d.join(e.broadcasts," ")},t.userAssetCb=function(e){t._view.getChild("DiamondText").asTextField.text=e.assetsQuantity.diamond.toString()},t.assetTransCb=function(e){"diamond"==e.assetCode&&(t._view.getChild("DiamondText").asTextField.text=e.quantity.toString()),l.default.alert("\u63d0\u793a\u4fe1\u606f",e.tips)},t.userCertCb=function(e){1==e.code&&(t._view.getChild("CertButton").asButton.visible=!1),t._view.getControllerAt(0).setSelectedIndex(0),l.default.alert("\u63d0\u793a\u4fe1\u606f",e.message)},t.updateTableNoCb=function(e){if(e.showInputTableNo(),6==e.tableNo.length){var n=f.default.getInst().getGameServiceByServiceId(t.getServiceId(parseInt(d.join(t.tableNo,""))));if(null==n)return void l.default.alert("\u63d0\u793a","\u670d\u52a1\u5668\u672a\u542f\u52a8\uff0c\u65e0\u6cd5\u8fdb\u884c\u6e38\u620f!");new p.default(n.address,n.port)}},t.getServiceId=function(e){var t=parseInt((e/1e3).toString());if(t<200)return t;for(;t>200;)t-=200;return t},t.loginSuccessCb=function(e){var n=d.join(t.tableNo,"");0==t.tableNo.length?_.default.use(s.default.GAME_WS_NAME).send(new g.default):_.default.use(s.default.GAME_WS_NAME).send(new v.default(Number(n)))},t}return r(t,e),t.prototype.onLoad=function(){var e=this;c.default.use(s.default.PLAZA_FIRE).onGroup("room",this.roomCb,this),c.default.use(s.default.PLAZA_FIRE).onGroup("room_record",this.roomRecordCb,this),c.default.use(s.default.PLAZA_FIRE).onGroup("broadcast",this.broadcastCb,this),c.default.use(s.default.PLAZA_FIRE).onGroup("user_asset",this.userAssetCb,this),c.default.use(s.default.PLAZA_FIRE).onGroup("user_cert",this.userCertCb,this),c.default.use(s.default.PLAZA_FIRE).onGroup("asset_trans",this.assetTransCb,this),c.default.use(s.default.PLAZA_FIRE).onGroup("recharge_record",this.rechargeRecordCb,this),c.default.use(s.default.LOCAL_FIRE).onGroup("update_table_no",this.updateTableNoCb,this),c.default.use(s.default.GAME_FIRE).onGroup("login_success",this.loginSuccessCb,this),this.loadPackage("plaza",function(){fgui.UIPackage.addPackage("plaza"),e._view=fgui.UIPackage.createObject("plaza","PlazaLayer").asCom,e.initService(),e.initView(),fgui.GRoot.inst.addChild(e._view)})},t.prototype.onInitAiJCom=function(e){_.default.use(s.default.PLAZA_WS_NAME).send(new C.default),_.default.use(s.default.PLAZA_WS_NAME).send(new R.default(["diamond","gold_coin","room_card"]))},t.prototype.onDestroy=function(){c.default.use(s.default.PLAZA_FIRE).offGroup(this),c.default.use(s.default.LOCAL_FIRE).offGroup(this),c.default.use(s.default.GAME_FIRE).offGroup(this),clearInterval(this._RoomEventInterval),this._view.dispose()},t.prototype.initView=function(){var e=this;this._view.getChild("NickNameText").asTextField.text=A.default.getInst().getMe().nickName,this._view.getChild("UserIdText").asTextField.text=A.default.getInst().getMe().showId,this._view.getChild("AvatarLoader").asLoader.url=A.default.getInst().getMe().avatar,this.initDistributorView(),this.initTransView(),this.initTransReviewView(),this.initCertView(),this.initGameCreateView(),this._view.getChild("AvatarLoader").asLoader.onClick(function(){var e=A.default.getInst().getMe();b.default.open(e.avatar,e.address,e.nickName,e.showId,e.ip)},this),this._view.getChild("n21").asButton.onClick(function(){_.default.use(s.default.PLAZA_WS_NAME).send(new y.default(1,10))},this),this._view.getChild("SettingButton").asButton.onClick(function(){I.default.setting(!0)},this),this._view.getChild("n4").asButton.onClick(function(){e.tableNo=[],e._view.getControllerAt(0).setSelectedIndex(2)},this),this._view.getChild("n6").asButton.onClick(function(){e.tableNo=[],c.default.use(s.default.LOCAL_FIRE).emit("update_table_no",e),e._view.getControllerAt(0).setSelectedIndex(4)},this);for(var t=function(t){var r=n._view.getChild("n68").asCom.getChild(t);r.asButton.onClick(function(){e.tableNo.length<6&&(e.tableNo.push(r.asButton.data),c.default.use(s.default.LOCAL_FIRE).emit("update_table_no",e))},n)},n=this,r=0,o=["n13","n14","n15","n16","n17","n18","n19","n20","n21","n22"];r<o.length;r++){t(o[r])}this._view.getChild("n68").asCom.getChild("n24").asButton.onClick(function(){e.tableNo=[],c.default.use(s.default.LOCAL_FIRE).emit("update_table_no",e)},this),this._view.getChild("n68").asCom.getChild("n25").asButton.onClick(function(){e.tableNo=d.dropRight(e.tableNo),c.default.use(s.default.LOCAL_FIRE).emit("update_table_no",e)},this)},t.prototype.initDistributorView=function(){var e=this;this._view.getChild("DistributorButton").asButton.visible=d.isString(A.default.getInst().getMe().distributorId)&&A.default.getInst().getMe().distributorId.length>0,this._view.getChild("DistributorButton").asButton.onClick(function(){e._view.getControllerAt(0).setSelectedIndex(5)},this);var t=this._view.getChild("distributor_group").asGroup;this._view.getChildInGroup("RechargeButton",t).asButton.onClick(function(){e._view.getControllerAt(0).setSelectedIndex(6)},this),this._view.getChildInGroup("RechargeRecordButton",t).asButton.onClick(function(){_.default.use(s.default.PLAZA_WS_NAME).send(new P.default(1,50,"room_card"))},this)},t.prototype.initTransView=function(){var e=this,t=this._view.getChild("recharge_group").asGroup,n=this._view.getChild("recharge_review_group").asGroup;this._view.getChildInGroup("RechargeReviewButton",t).asButton.onClick(function(){var r=e._view.getChildInGroup("RechargeUserIdTextField",t).asTextField.text,o=e._view.getChildInGroup("RechargeNumberTextField",t).asTextField.text;!d.isNumber(d.toNumber(o))||parseInt(o)<1?l.default.alert("\u63d0\u793a\u4fe1\u606f","\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u6570\u91cf!"):!d.isNumber(d.toNumber(r))||parseInt(r)<1?l.default.alert("\u63d0\u793a\u4fe1\u606f","\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u73a9\u5bb6ID!"):(e._view.getChildInGroup("RechargeAvatarLoader",n).asLoader.url="",e._view.getChildInGroup("RechargeNickNameText",n).asTextField.text="",e._view.getChildInGroup("RechargeUserIdText",n).asTextField.text=r,e._view.getChildInGroup("RechargeNumberText",n).asTextField.text=o,e._view.getControllerAt(0).setSelectedIndex(7))},this),this._view.getChildInGroup("RechargeCancelButton",t).asButton.onClick(function(){e._view.getControllerAt(0).setSelectedIndex(5)},this)},t.prototype.initTransReviewView=function(){var e=this,t=this._view.getChild("recharge_review_group").asGroup,n=this._view.getChild("recharge_group").asGroup;this._view.getChildInGroup("RechargeSubmitButton",t).asButton.onClick(function(){var t=e._view.getChildInGroup("RechargeUserIdTextField",n).asTextField.text,r=e._view.getChildInGroup("RechargeNumberTextField",n).asTextField.text;!d.isNumber(d.toNumber(r))||parseInt(r)<1?l.default.alert("\u63d0\u793a\u4fe1\u606f","\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u6570\u91cf!"):!d.isNumber(d.toNumber(t))||parseInt(t)<1?l.default.alert("\u63d0\u793a\u4fe1\u606f","\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u73a9\u5bb6ID!"):(_.default.use(s.default.PLAZA_WS_NAME).send(new O.default("room_card",t,parseInt(r))),e._view.getControllerAt(0).setSelectedIndex(5))},this)},t.prototype.initCertView=function(){var e=this;this._view.getChild("CertButton").asButton.visible=null==A.default.getInst().getMe().certStatus;var t=this._view.getChild("certification_group").asGroup;this._view.getChildInGroup("SubmitCertButton",t).asButton.onClick(function(){var n=e._view.getChildInGroup("CertNameTextField",t).asTextField.text,r=e._view.getChildInGroup("CertCardTextField",t).asTextField.text,o=e._view.getChildInGroup("CertMobileTextField",t).asTextField.text;n.length<2?l.default.alert("\u63d0\u793a\u4fe1\u606f","\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u59d3\u540d!"):r.length<6?l.default.alert("\u63d0\u793a\u4fe1\u606f","\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u8bc1\u4ef6\u53f7\u7801!"):o.length<6?l.default.alert("\u63d0\u793a\u4fe1\u606f","\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u624b\u673a\u53f7\u7801!"):_.default.use(s.default.PLAZA_WS_NAME).send(new E.default(n,r,o,"1"))},this)},t.prototype.initGameCreateView=function(){var e=this,t=this._view.getChild("create_group").asGroup;this._view.getChildInGroup("GameTypeList",t).asList.getChild("mahjong").asButton.onClick(function(){e._view.getControllerAt(0).setSelectedIndex(2)},this),this._view.getChildInGroup("GameTypeList",t).asList.getChild("poker").asButton.onClick(function(){e._view.getControllerAt(0).setSelectedIndex(3)},this),this._view.getChildInGroup("CreateSubGameButton",t).asButton.onClick(function(){var e=f.default.getInst().randomGameService("\u5357\u4e30\u9ebb\u5c06");null==e?l.default.alert("\u63d0\u793a","\u5357\u4e30\u9ebb\u5c06\u670d\u52a1\u5668\u672a\u542f\u52a8\uff0c\u65e0\u6cd5\u8fdb\u884c\u6e38\u620f!"):new p.default(e.address,e.port)},this)},t.prototype.showInputTableNo=function(){var e=this;d.each(["n31","n32","n33","n34","n35","n36"],function(t,n){n<e.tableNo.length?e._view.getChild("n68").asCom.getChild(t).asTextField.text=e.tableNo[n]:e._view.getChild("n68").asCom.getChild(t).asTextField.text=""})},t.prototype.initService=function(){h.default.getInst()._aiJPro.send(new u.default),this._RoomEventInterval=window.setInterval(function(){h.default.getInst()._aiJPro.send(new u.default)},3e4)},t=o([i],t)}(m.default);n.default=M,cc._RF.pop()},{"../AiJCCComponent":"AiJCCComponent","../AlertWindow":"AlertWindow","../AppConfig":"AppConfig","../GameServiceManager":"GameServiceManager","../SettingWindow":"SettingWindow","../UIManger":"UIManger","../UserInfoWindow":"UserInfoWindow","../fire/FireKit":"FireKit","../hero/HeroManager":"HeroManager","../plazz/PlazaConfig":"PlazaConfig","../plazz/event/RoomEvent":"RoomEvent","../room/event/CreateTableEvent":"CreateTableEvent","../room/event/JoinTableEvent":"JoinTableEvent","../room/mahjong/MahjongRoomConfig":"MahjongRoomConfig","../ws/AiJKit":"AiJKit","./RechargeRecordLayer":"RechargeRecordLayer","./RoomRecordLayer":"RoomRecordLayer","./event/BroadcastEvent":"BroadcastEvent","./event/RechargeRecordEvent":"RechargeRecordEvent","./event/RoomRecordEvent":"RoomRecordEvent","./event/UserAssetEvent":"UserAssetEvent","./event/UserAssetTransEvent":"UserAssetTransEvent","./event/UserCertEvent":"UserCertEvent",lodash:6}],PlazaLoginEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"6a116xSU/9HAbU9wLi9C7Iz","PlazaLoginEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],PlazaLoginHandler:[function(e,t,n){"use strict";cc._RF.push(t,"79f07eBmtJJI49YPb5XxnWJ","PlazaLoginHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).fire("login_success",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],PlazaMobileLoginEvent:[function(e,t,n){"use strict";cc._RF.push(t,"34f259CczdIeINvGbPnfeEl","PlazaMobileLoginEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t,n){var r=e.call(this)||this;return r.mobile=t,r.password=n,r.mainType=1,r.subType=1,r}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],PlazaWeiXinLoginEvent:[function(e,t,n){"use strict";cc._RF.push(t,"d12caDKsTBFO50sPctvNtoJ","PlazaWeiXinLoginEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t){var n=e.call(this)||this;return n.code=t,n.mainType=1,n.subType=3,n}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],PlazaWsListener:[function(e,t,n){"use strict";cc._RF.push(t,"2f07eMZunRE3JN8FW+9wuYA","PlazaWsListener"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("../fire/FireKit"),o=e("../AppConfig"),i=e("../LoadingWindow"),a=function(){function e(){}return e.prototype.onClose=function(e,t){console.log("websocket close"),i.default.close()},e.prototype.onConnecting=function(e){i.default.loading("\u6b63\u5728\u8fde\u63a5\u670d\u52a1\u5668!")},e.prototype.onError=function(e,t){},e.prototype.onForcedClose=function(e,t){i.default.close()},e.prototype.onMessage=function(e,t){},e.prototype.onOpen=function(e,t,n){i.default.close(),r.default.use(o.default.LOCAL_FIRE).fire("login")},e.prototype.onReconnectAttempt=function(e,t){i.default.loading("\u7f51\u7edc\u8fde\u63a5\u5f02\u5e38\uff0c\u91cd\u65b0\u8fde\u63a5\u4e2d...\n\u7b2c"+t+"\u6b21\u91cd\u8bd5")},e.prototype.onReconnectFail=function(e,t){i.default.close(),r.default.use(o.default.PLAZA_FIRE).emit("ws_error")},e.prototype.onTimeout=function(e){},e}();n.default=a,cc._RF.pop()},{"../AppConfig":"AppConfig","../LoadingWindow":"LoadingWindow","../fire/FireKit":"FireKit"}],RechargeRecordEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"978cfDjc9xHdZ36wE8YX+zB","RechargeRecordEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).fire("recharge_record",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],RechargeRecordEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"fd906GStzdJAKt68hp1isox","RechargeRecordEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o;cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RechargeRecordEvent:[function(e,t,n){"use strict";cc._RF.push(t,"aa95fCR2/9Mw7+80DJnEBBy","RechargeRecordEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t,n,r){var o=e.call(this)||this;return o.mainType=4,o.subType=3,o.page=t,o.pageSize=n,o.assetCode=r,o}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RechargeRecordLayer:[function(e,t,n){"use strict";cc._RF.push(t,"9f573nh+UBN9oTeHbelqGEz","RechargeRecordLayer");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var i=e("../AiJCCComponent"),a=cc._decorator.ccclass,s=e("lodash"),u=e("../hero/HeroManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.onLoad=function(){var e=this;this.loadPackage("plaza",function(){fgui.UIPackage.addPackage("plaza"),e._view=fgui.UIPackage.createObject("plaza","RechargeRecordLayer").asCom,e.initView(),fgui.GRoot.inst.addChild(e._view)})},t.prototype.onInitAiJCom=function(e){var t=this,n=e;s.each(n.rechargeRecords,function(e){var n=fgui.UIPackage.createObject("plaza","RechargeRecordComponent").asCom;n.getChild("TransText").asTextField.text=e.sellerId==u.default.getInst().getMe().userId?"\u5356":"\u4e70",n.getChild("UserIdText").asTextField.text=s.padStart(e.sellerId==u.default.getInst().getMe().userId?e.buyerId:e.sellerId,8,"0"),n.getChild("NickNameText").asTextField.text=e.sellerId==u.default.getInst().getMe().userId?e.buyerName:e.sellerName,n.getChild("AssetNumberText").asTextField.text=e.quantity.toString(),n.getChild("CreatedTimeText").asTextField.text=e.createdTime,t._rechargeRecordList.addChildAt(n)})},t.prototype.initView=function(){var e=this;this._view.getChild("BackButton").asButton.onClick(function(){e.destroy()},this),this._rechargeRecordList=this._view.getChild("RechargeRecordList").asList,this._rechargeRecordList.removeChildren()},t.prototype.onDestroy=function(){this._view.dispose()},t=o([a],t)}(i.default);n.default=c,cc._RF.pop()},{"../AiJCCComponent":"AiJCCComponent","../hero/HeroManager":"HeroManager",lodash:6}],RoomCommonResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"dbc8clNljlD/JJmRR4fGiVs","RoomCommonResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../AlertWindow"),a=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.alert("\u63d0\u793a\u4fe1\u606f",t.message)},t}(o.AiJ.ResponseHandler);n.default=a,cc._RF.pop()},{"../../AlertWindow":"AlertWindow","../../ws/AiJ":"AiJ"}],RoomEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"7bffbONpaRDO7rBIX/oqcNZ","RoomEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).emit("room",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],RoomEvent:[function(e,t,n){"use strict";cc._RF.push(t,"50762pUNXhELq25onfmoGim","RoomEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.mainType=3,t.subType=1,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RoomLoginEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"ed6bcNqupdLe5uwElIM2WRM","RoomLoginEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RoomLoginResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"6b8e9ySsnNK3YkB8YILd4G/","RoomLoginResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.GAME_FIRE).emit("login_success",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],RoomMobileLoginEvent:[function(e,t,n){"use strict";cc._RF.push(t,"5b50bjEbJ9Mu7BHPT/NgHRx","RoomMobileLoginEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t,n){var r=e.call(this)||this;return r.mobile=t,r.password=n,r.mainType=1,r.subType=1,r}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RoomRecordEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"ec6d7NJ1RtMerrwVmPu4+of","RoomRecordEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).fire("room_record",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],RoomRecordEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"16ae2NgkKxA8q4w5EGSAsV4","RoomRecordEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o;var i=function(){return function(){}}();n.RoomRecord=i;var a=function(){return function(){}}();n.RoomRecordSummary=a,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RoomRecordEvent:[function(e,t,n){"use strict";cc._RF.push(t,"03167sDvK5FVYYFI5molf+J","RoomRecordEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t,n){var r=e.call(this)||this;return r.page=t,r.pageSize=n,r.mainType=3,r.subType=2,r}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RoomRecordLayer:[function(e,t,n){"use strict";cc._RF.push(t,"95b26Hb62tBoIK7+JM1fkyp","RoomRecordLayer");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var i=e("../AiJCCComponent"),a=cc._decorator.ccclass,s=e("lodash"),u=e("../UIManger"),c=e("./PlazaLayer"),f=e("../room/mahjong/MahjongVideoLayer"),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t.prototype.onLoad=function(){var e=this;this.loadPackage("plaza",function(){fgui.UIPackage.addPackage("plaza"),e._view=fgui.UIPackage.createObject("plaza","RoomRecordLayer").asCom,e.initView(),fgui.GRoot.inst.addChild(e._view)})},t.prototype.onInitAiJCom=function(e){var t=this,n=e;s.each(n.roomRecords,function(e){var n=fgui.UIPackage.createObject("plaza","RoomRecordComponent").asCom;n.getChild("ServiceNameText").asTextField.text=e.serviceName,n.getChild("TableNoText").asTextField.text=e.tableNo.toString(),n.getChild("NickNameText").asTextField.text=e.nickName,n.getChild("ScoreText").asTextField.text=(e.score>=0?"+":"")+e.score.toString(),n.getChild("StartedTimeText").asTextField.text=e.startedTime,n.getChild("DetailButton").asButton.data=e,n.getChild("DetailButton").asButton.onClick(function(e){t._roomRecordItemList.removeChildren();var n=fgui.GObject.cast(e.currentTarget);t._currRoomRecord=n.data;var r=JSON.parse(t._currRoomRecord.summary);s.each(r,function(e,n){var r=fgui.UIPackage.createObject("plaza","RoomRecordItemComponent").asCom;r.getChild("NumberText").asTextField.text=(n+1).toString(),r.getChild("PlayVideoButton").asButton.data={index:n,detail:t._currRoomRecord.detail},r.getChild("PlayVideoButton").asButton.onClick(function(e){var t=fgui.GObject.cast(e.currentTarget).data;u.default.getInst().switchLayer(f.default,t,!1)},t);var o=r.getChild("RoomRecordItemScoreList").asList;o.removeChildren(),s.each(e,function(e,t){var n=fgui.UIPackage.createObject("plaza","RoomRecordItemScoreComponent").asCom;n.getChild("NickNameText").asTextField.text=e.nickName,n.getChild("ScoreText").asTextField.text=(e.score>=0?"+":"")+e.score,o.addChildAt(n)}),t._roomRecordItemList.addChildAt(r)}),t._view.getController("c1").setSelectedIndex(1)},t),t._roomRecordList.addChildAt(n)})},t.prototype.initView=function(){this._view.getChild("BackButton").asButton.onClick(function(){u.default.getInst().switchLayer(c.default)},this),this._roomRecordList=this._view.getChild("RoomRecordList").asList,this._roomRecordItemList=this._view.getChildInGroup("RoomRecordItemList",this._view.getChild("RoomRecordItemGroup").asGroup).asList,this._roomRecordList.removeChildren()},t.prototype.onDestroy=function(){this._view.dispose()},t=o([a],t)}(i.default);n.default=l,cc._RF.pop()},{"../AiJCCComponent":"AiJCCComponent","../UIManger":"UIManger","../room/mahjong/MahjongVideoLayer":"MahjongVideoLayer","./PlazaLayer":"PlazaLayer",lodash:6}],RoomServiceEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"ee792OsYHhMJJYuQH8IziCo","RoomServiceEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o;var i=function(){return function(){}}();n.RoomItem=i,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],RoomWsListener:[function(e,t,n){"use strict";cc._RF.push(t,"80c6abBpPtLR5wCEbbZp1TX","RoomWsListener"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("../fire/FireKit"),o=e("../AppConfig"),i=e("../LoadingWindow"),a=function(){function e(){}return e.prototype.onClose=function(e,t){i.default.close()},e.prototype.onConnecting=function(e){i.default.loading("\u6b63\u5728\u8fde\u63a5\u670d\u52a1\u5668!")},e.prototype.onError=function(e,t){},e.prototype.onForcedClose=function(e,t){},e.prototype.onMessage=function(e,t){},e.prototype.onOpen=function(e,t,n){r.default.use(o.default.GAME_FIRE).emit("open")},e.prototype.onReconnectAttempt=function(e,t){i.default.loading("\u7f51\u7edc\u8fde\u63a5\u5f02\u5e38\uff0c\u91cd\u65b0\u8fde\u63a5\u4e2d...\n\u7b2c"+t+"\u6b21\u91cd\u8bd5")},e.prototype.onReconnectFail=function(e,t){i.default.close(),r.default.use(o.default.PLAZA_FIRE).emit("ws_error")},e.prototype.onTimeout=function(e){},e}();n.default=a,cc._RF.pop()},{"../AppConfig":"AppConfig","../LoadingWindow":"LoadingWindow","../fire/FireKit":"FireKit"}],SettingWindow:[function(e,t,n){"use strict";cc._RF.push(t,"376d1ULxx5JGZRw9LPkVq2v","SettingWindow");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("./UIManger"),i=e("./WelcomeLayer"),a=e("./Setting"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.getInst=function(){return null==t.inst&&(t.inst=new t),t.inst},t.setting=function(e){void 0===e&&(e=!1);var n=t.getInst();n.show(),n.contentPane.getChild("AccountGroup").asGroup.visible=e},t.prototype.onInit=function(){var e=this;this.contentPane=fgui.UIPackage.createObject("commons","SettingWindow").asCom;var t=this.contentPane.getChild("AccountGroup").asGroup;this.contentPane.getChildInGroup("AccountToggleButton",t).asButton.onClick(function(){cc.sys.localStorage.setItem("user",""),o.default.getInst().switchLayer(i.default)},this),this.contentPane.getChildInGroup("ExitGameButton",t).asButton.onClick(function(){cc.director.end()},this),this.contentPane.getChild("MusicToggleButton").asButton.selected=a.default.getMusic(),this.contentPane.getChild("SoundToggleButton").asButton.selected=a.default.getSound(),this.contentPane.getChild("MusicToggleButton").asButton.onClick(function(){a.default.setMusic(e.contentPane.getChild("MusicToggleButton").asButton.selected)},this),this.contentPane.getChild("SoundToggleButton").asButton.onClick(function(){a.default.setSound(e.contentPane.getChild("SoundToggleButton").asButton.selected)},this),this.center()},t}(fgui.Window);n.default=s,cc._RF.pop()},{"./Setting":"Setting","./UIManger":"UIManger","./WelcomeLayer":"WelcomeLayer"}],Setting:[function(e,t,n){"use strict";cc._RF.push(t,"2a17cMeaodJ5Yp+f8XLiaqs","Setting"),Object.defineProperty(n,"__esModule",{value:!0});var r=function(){function e(){}return e.setMusic=function(e){this.set("music",e)},e.getMusic=function(){return null==this.get("music")||this.get("music")},e.setSound=function(e){this.set("sound",e)},e.getSound=function(){return null==this.get("sound")||this.get("sound")},e.set=function(e,t){cc.sys.localStorage.setItem(e,t)},e.get=function(e){return cc.sys.localStorage.getItem(e)},e}();n.default=r,cc._RF.pop()},{}],SitDownTableEvent:[function(e,t,n){"use strict";cc._RF.push(t,"6001bxoqg5Iobu52ySnieC5","SitDownTableEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.mainType=2,t.subType=7,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],StandUpTableEvent:[function(e,t,n){"use strict";cc._RF.push(t,"a8925kxw8dJB52DG8ejZlo+","StandUpTableEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=e.call(this)||this;return t.mainType=2,t.subType=8,t}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],UIManger:[function(e,t,n){"use strict";cc._RF.push(t,"549a8k6I+RJW58dWYRmLs1o","UIManger"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("./AiJCCComponent"),o=e("async-lock"),i=function(){function e(){this.lock=new o}return e.getInst=function(){return null==e.inst&&(e.inst=new e),this.inst},e.prototype.setRoot=function(e){this.root=e},e.prototype.switchLayer=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n=!0),this.preLayer=this.currentLayer,this.currentLayer=this.root.addComponent(e),this.currentLayer instanceof r.default&&this.currentLayer.initAiJCom(t),n&&this.destroyPreLayer()},e.prototype.destroyPreLayer=function(){null!=this.preLayer&&this.preLayer.destroy(),this.preLayer=null},e}();n.default=i,cc._RF.pop()},{"./AiJCCComponent":"AiJCCComponent","async-lock":1}],UserAssetEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"b3c4azvdqNAl6SR9n7knMwJ","UserAssetEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).fire("user_asset",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],UserAssetEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"d4de0shmdtA3IW/vFv5dauU","UserAssetEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.assetsQuantity={},t}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],UserAssetEvent:[function(e,t,n){"use strict";cc._RF.push(t,"14c2dv3GolLHL5/Y5FMeb4T","UserAssetEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t){var n=e.call(this)||this;return n.assetCodes=t,n.mainType=4,n.subType=1,n}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],UserAssetTransEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"dfdb4m6BM1CnoBKtkNkFJCV","UserAssetTransEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).fire("asset_trans",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],UserAssetTransEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"be7f2p+Tu5DRqGijXvmuOj7","UserAssetTransEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],UserAssetTransEvent:[function(e,t,n){"use strict";cc._RF.push(t,"6da1byS729IT51pzHrRSzOY","UserAssetTransEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t,n,r){var o=e.call(this)||this;return o.mainType=4,o.subType=2,o.assetCode=t,o.buyerId=n,o.quantity=r,o}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],UserCertEventResponseHandler:[function(e,t,n){"use strict";cc._RF.push(t,"414909wZYJCi6NM+3AVt+IK","UserCertEventResponseHandler");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../ws/AiJ"),i=e("../../fire/FireKit"),a=e("../../AppConfig"),s=function(e){function t(){return e.call(this)||this}return r(t,e),t.prototype.handler=function(e,t){i.default.use(a.default.PLAZA_FIRE).fire("user_cert",t)},t}(o.AiJ.ResponseHandler);n.default=s,cc._RF.pop()},{"../../AppConfig":"AppConfig","../../fire/FireKit":"FireKit","../../ws/AiJ":"AiJ"}],UserCertEventResponse:[function(e,t,n){"use strict";cc._RF.push(t,"5e269SfphFLsYTJV8AdXAyz","UserCertEventResponse");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return r(t,e),t}(e("../../ws/AiJ").AiJ.Response);n.UserCertEventResponse=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],UserCertEvent:[function(e,t,n){"use strict";cc._RF.push(t,"2f259rMBJdEQqlAzXQytN6T","UserCertEvent");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=function(e){function t(t,n,r,o){var i=e.call(this)||this;return i.mainType=4,i.subType=4,i.certName=t,i.certCard=n,i.certMobile=r,i.certType=o,i}return r(t,e),t}(e("../../ws/AiJ").AiJ.AiJEvent);n.default=o,cc._RF.pop()},{"../../ws/AiJ":"AiJ"}],UserInfoWindow:[function(e,t,n){"use strict";cc._RF.push(t,"f7c66ZdEHhM+qOYHcP6iN3w","UserInfoWindow");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(n,"__esModule",{value:!0});var o=e("lodash"),i=function(e){function t(){return e.call(this)||this}return r(t,e),t.getInst=function(){return null==t.inst&&(t.inst=new t),t.inst},t.open=function(e,n,r,i,a){var s=t.getInst();s.show(),s.contentPane.getChild("UserAddressText").asTextField.text=n,s.contentPane.getChild("NickNameText").asTextField.text=r,s.contentPane.getChild("UserIdText").asTextField.text=o.padStart(i,8,"0"),s.contentPane.getChild("UserIpText").asTextField.text=a,s.contentPane.getChild("AvatarLoader").asLoader.url=e},t.prototype.onInit=function(){this.contentPane=fgui.UIPackage.createObject("commons","UserInfoWindow").asCom,this.center()},t}(fgui.Window);n.default=i,cc._RF.pop()},{lodash:6}],WelcomeLayer:[function(e,t,n){"use strict";cc._RF.push(t,"6eae7xQrxhBRr15FAuzxirJ","WelcomeLayer");var r=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),o=this&&this.__decorate||function(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var i=cc._decorator.ccclass,a=e("./AppConfig"),s=e("./AlertWindow"),u=e("./fire/FireKit"),c=e("./plazz/PlazaConfig"),f=e("./UIManger"),l=e("./plazz/PlazaLayer"),p=e("./AiJCCComponent"),h=e("./hero/HeroManager"),d=e("./hero/Hero"),_=e("./AudioManager"),g=e("./WxHelper"),v=e("./hotupdate/HotUpdateManager"),y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.loginSuccessCb=function(e){h.default.getInst().setMe(new d.default(e.userName,e.showId,e.userId,e.nickName,e.gender,e.avatar,e.distributorId,e.address,e.longitude,e.latitude,e.ip,e.certStatus)),f.default.getInst().switchLayer(l.default)},t.ws_error=function(){s.default.alert("\u63d0\u793a\u4fe1\u606f","\u7f51\u7edc\u9519\u8bef\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5\uff01")},t}return r(t,e),t.prototype.onLoad=function(){var e=this;u.default.use(a.default.PLAZA_FIRE).onGroup("ws_error",this.ws_error,this),u.default.use(a.default.PLAZA_FIRE).onGroup("login_success",this.loginSuccessCb,this),this.loadPackage("welcome",function(){fgui.UIPackage.addPackage("welcome"),e._view=fgui.UIPackage.createObject("welcome","WelcomeLayer").asCom,e._hotUpdateGroup=e._view.getChild("hotUpdateGroup").asGroup,e._hotUpdateProgressBar=e._view.getChildInGroup("hotUpdateProgressBar",e._hotUpdateGroup).asProgress,e.initView(),fgui.GRoot.inst.addChild(e._view)})},t.prototype.onInitAiJCom=function(e){var t=this;c.default.init(a.default.PLAZA_WS_HOST,a.default.PLAZA_WS_PORT),_.default.play_music("commons","bgm"),cc.sys.isNative&&(cc.log("hot update manager check"),v.default.getInst().checkAndUpdate(function(e){switch(e.code){case 0:t._view.getControllerAt(0).setSelectedIndex(1);break;case 1:var n=e.downloaded,r=e.total;t._hotUpdateProgressBar.max=r,t._hotUpdateProgressBar.value=n;break;case 2:t._view.getControllerAt(0).setSelectedIndex(0)}}))},t.prototype.onDestroy=function(){u.default.use(a.default.PLAZA_FIRE).offGroup(this),this._view.dispose()},t.prototype.initView=function(){var e=this;this._view.getChild("username").asTextInput.text="15000000004",this._view.getChild("password").asTextInput.text="123456",this._view.getChild("login").asButton.onClick(function(){c.default.getInst()._aiJPro.isOpen()?(cc.sys.localStorage.setItem("user",JSON.stringify({username:e.username(),password:e.password()})),u.default.use(a.default.LOCAL_FIRE).emit("login")):s.default.alert("\u63d0\u793a\u4fe1\u606f","\u672a\u8fde\u63a5\u670d\u52a1\u5668\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5\uff01")},this),this._view.getChild("wx_login").asButton.onClick(function(){g.default.wxLogin()},this)},t.prototype.username=function(){return this._view.getChild("username").asTextInput.text},t.prototype.password=function(){return this._view.getChild("password").asTextInput.text},t=o([i],t)}(p.default);n.default=y,cc._RF.pop()},{"./AiJCCComponent":"AiJCCComponent","./AlertWindow":"AlertWindow","./AppConfig":"AppConfig","./AudioManager":"AudioManager","./UIManger":"UIManger","./WxHelper":"WxHelper","./fire/FireKit":"FireKit","./hero/Hero":"Hero","./hero/HeroManager":"HeroManager","./hotupdate/HotUpdateManager":"HotUpdateManager","./plazz/PlazaConfig":"PlazaConfig","./plazz/PlazaLayer":"PlazaLayer"}],WxHelper:[function(e,t,n){"use strict";cc._RF.push(t,"2df913Ps7BMyZmC4wfyLsXJ","WxHelper"),Object.defineProperty(n,"__esModule",{value:!0});var r=e("./ws/AiJKit"),o=e("./AppConfig"),i=e("./plazz/event/PlazaWeiXinLoginEvent"),a=e("./plazz/PlazaConfig"),s=e("./AlertWindow"),u=function(){function e(){}return e.wxLogin=function(){cc.sys.os==cc.sys.OS_ANDROID&&jsb.reflection.callStaticMethod("com/xiyoufang/aij/wx/WxHelper","wxLogin","()V")},e.onWxLogin=function(e){cc.log("code:"+e),a.default.getInst()._aiJPro.isOpen()?r.default.use(o.default.PLAZA_WS_NAME).send(new i.default(e)):s.default.alert("\u63d0\u793a\u4fe1\u606f","\u672a\u8fde\u63a5\u670d\u52a1\u5668\uff0c\u8bf7\u7a0d\u540e\u518d\u8bd5\uff01")},e}();n.default=u,cc.WxHelper=u,cc._RF.pop()},{"./AlertWindow":"AlertWindow","./AppConfig":"AppConfig","./plazz/PlazaConfig":"PlazaConfig","./plazz/event/PlazaWeiXinLoginEvent":"PlazaWeiXinLoginEvent","./ws/AiJKit":"AiJKit"}],"use_v2.1-2.2.1_cc.Toggle_event":[function(e,t,n){"use strict";cc._RF.push(t,"38217Yq3FpBObE4U186jHUB","use_v2.1-2.2.1_cc.Toggle_event"),cc.Toggle&&(cc.Toggle._triggerEventInScript_isChecked=!0),cc._RF.pop()},{}]},{},["AiJApp","AiJCCComponent","AlertWindow","AppConfig","AudioManager","GameServiceManager","LoadingWindow","Setting","SettingWindow","UIManger","UserInfoWindow","WelcomeLayer","WxHelper","FireKit","OnFire","Hero","HeroManager","HotUpdateManager","PlazaConfig","PlazaLayer","PlazaWsListener","RechargeRecordLayer","RoomRecordLayer","BroadcastEvent","PlazaMobileLoginEvent","PlazaWeiXinLoginEvent","RechargeRecordEvent","RoomEvent","RoomRecordEvent","UserAssetEvent","UserAssetTransEvent","UserCertEvent","BroadcastEventResponseHandler","PlazaCommonResponseHandler","PlazaLoginHandler","RechargeRecordEventResponseHandler","RoomEventResponseHandler","RoomRecordEventResponseHandler","UserAssetEventResponseHandler","UserAssetTransEventResponseHandler","UserCertEventResponseHandler","BroadcastEventResponse","PlazaLoginEventResponse","RechargeRecordEventResponse","RoomRecordEventResponse","RoomServiceEventResponse","UserAssetEventResponse","UserAssetTransEventResponse","UserCertEventResponse","AbstractRoomConfig","RoomWsListener","ClientReadyEvent","CreateTableEvent","DismissTableEvent","DismissVoteTableEvent","HeroProfileEvent","JoinTableEvent","LeaveTableEvent","RoomMobileLoginEvent","SitDownTableEvent","StandUpTableEvent","ChatEventResponseHandler","CreateTableEventResponseHandler","DismissVoteEventResponseHandler","HeroEnterEventResponseHandler","HeroLeaveEventResponseHandler","HeroOfflineEventResponseHandler","HeroOnlineEventResponseHandler","HeroProfileEventResponseHandler","HeroSceneResponseHandler","HeroSitDownEventResponseHandler","HeroStandUpEventResponseHandler","JoinTableEventResponseHandler","LoginNotifyResponseHandler","RoomCommonResponseHandler","RoomLoginResponseHandler","MahjongGameEngine","MahjongGameLayer","MahjongRoomConfig","MahjongVideoLayer","MahjongOperateEvent","MahjongOutCardEvent","MahjongDispathCardResponseHandler","MahjongEndEventResponseHandler","MahjongErrorEventResponseHandler","MahjongGameEndEventResponseHandler","MahjongGameStartResponseHandler","MahjongGameStatusResponseHandler","MahjongOperateNotifyEventResponseHandler","MahjongOperateResultEventResponseHandler","MahjongOutCardResponseHandler","MahjongPlayingGameSceneResponseHandler","MahjongPrepareGameSceneResponseHandler","MahjongAction","MahjongGameActionRecord","MahjongGameRecord","MahjongGameStartRecord","MahjongPlayerRecord","MahjongRecord","MahjongDispatchCardEventResponse","MahjongEndEventResponse","MahjongErrorEventResponse","MahjongGameEndEventResponse","MahjongGameStartEventResponse","MahjongGameStatusResponse","MahjongOperateNotifyEventResponse","MahjongOperateResultEventResponse","MahjongOutCardEventResponse","MahjongPlayingGameSceneResponse","MahjongPrepareGameSceneResponse","MahjongWeaveItem","MahjongWeaveType","CreateTableEventResponse","DismissVoteEventResponse","HeroEnterEventResponse","HeroLeaveEventResponse","HeroOfflineEventResponse","HeroOnlineEventResponse","HeroProfileEventResponse","HeroSceneResponse","HeroSitDownEventResponse","HeroStandUpEventResponse","JoinTableEventResponse","RoomLoginEventResponse","AiJ","AiJKit","AiJPro","use_v2.1-2.2.1_cc.Toggle_event"]);